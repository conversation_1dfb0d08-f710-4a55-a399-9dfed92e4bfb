import Image from "next/image";
import Link from "next/link";

interface LogoProps {
  href?: string;
  size?: "sm" | "md" | "lg" | "xl";
  showText?: boolean;
  className?: string;
}

export default function Logo({
  href = "/",
  size = "md",
  showText = true,
  className = ""
}: LogoProps) {
  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-8 h-8",
    lg: "w-12 h-12",
    xl: "w-20 h-20"
  };

  const textSizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl",
    xl: "text-3xl"
  };

  // Get logo source based on theme - use dark logo for V1, regular for V2
  const getLogoSrc = () => {
    if (typeof window !== 'undefined') {
      const isDarkTheme = document.body.classList.contains('theme-v1') || document.body.classList.contains('dark');
      return isDarkTheme ? "/tenxcfo-dark.png" : "/tenxcfo.png";
    }
    return "/tenxcfo-dark.png"; // Default to dark for SSR
  };

  const logoSrc = getLogoSrc();
  const sizeValue = size === "sm" ? 24 : size === "md" ? 32 : size === "lg" ? 48 : 80;

  const logoContent = (
    <div className={`flex items-center space-x-3 ${className}`}>
      <div className={`${sizeClasses[size]} relative`}>
        <Image
          src={logoSrc}
          alt="TenxCFO Logo"
          width={sizeValue}
          height={sizeValue}
          className="object-contain"
          priority
        />
      </div>
      {showText && (
        <span className={`${textSizeClasses[size]} font-bold text-white`}>
          TenxCFO
        </span>
      )}
    </div>
  );

  if (href) {
    return (
      <Link href={href} className="flex items-center">
        {logoContent}
      </Link>
    );
  }

  return logoContent;
}
