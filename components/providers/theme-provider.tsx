"use client";

import { useThemeStore } from "@/stores/themeStore";
import { useEffect } from "react";

export default function ThemeProvider({ children }: { children: React.ReactNode }) {
  const { version, setHydrated } = useThemeStore();

  useEffect(() => {
    // Apply theme after component mounts to avoid hydration mismatch
    const applyTheme = () => {
      if (typeof window === 'undefined') return;
      
      const body = document.body;
      
      // Remove existing theme classes
      body.classList.remove('theme-v1', 'theme-v2', 'light', 'dark');
      
      // Add new theme class
      body.classList.add(`theme-${version}`);
      
      // Also apply light/dark class for existing CSS variables
      if (version === 'v2') {
        body.classList.add('light');
      } else {
        body.classList.add('dark');
      }
      
      // Mark as hydrated
      setHydrated();
    };

    applyTheme();
  }, [version, setHydrated]);

  return <>{children}</>;
}
