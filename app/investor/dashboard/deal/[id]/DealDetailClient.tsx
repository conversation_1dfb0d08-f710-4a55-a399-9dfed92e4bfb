"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ThemeToggle from "@/components/ui/theme-toggle";
import { useAuthStore } from "@/stores/authStore";
import {
    ArrowLeft,
    Bell,
    DollarSign,
    Download,
    Heart,
    LogOut,
    MessageCircle,
    Settings,
    Users
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

interface DealDetail {
  id: number;
  companyName: string;
  industry: string;
  location: string;
  fundingGoal: string;
  financialScore: number;
  yearEstablished: number;
  employees: string;
  revenue: string;
  status: string;
  description: string;
  tags: string[];
  interested: number;
  viewed: number;
  detailedDescription: string;
  businessModel: string;
  marketSize: string;
  competition: string;
  teamSize: number;
  foundersInfo: string;
  financials: {
    currentRevenue: string;
    projectedRevenue: string;
    burnRate: string;
    runway: string;
  };
  documents: string[];
}

export default function DealDetailClient() {
  const { user } = useAuthStore();
  const params = useParams();
  const dealId = params.id as string;
  const [deal, setDeal] = useState<DealDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - replace with actual API call
    const mockDeal: DealDetail = {
      id: parseInt(dealId),
      companyName: "TechCorp Solutions",
      industry: "Technology",
      location: "Bangalore",
      fundingGoal: "$1.2M",
      financialScore: 85,
      yearEstablished: 2020,
      employees: "25-50",
      revenue: "$3.2M",
      status: "trending",
      description: "AI-powered customer service automation platform",
      tags: ["AI/ML", "B2B", "SaaS"],
      interested: 15,
      viewed: 234,
      detailedDescription: "TechCorp Solutions is revolutionizing customer service with our AI-powered automation platform. We help businesses reduce response times by 80% while maintaining high customer satisfaction scores.",
      businessModel: "SaaS subscription model with tiered pricing based on usage and features",
      marketSize: "$50B global customer service automation market",
      competition: "Competing with established players like Zendesk and Freshworks, but with superior AI capabilities",
      teamSize: 35,
      foundersInfo: "Founded by ex-Google and Microsoft engineers with 15+ years of experience in AI and customer service",
      financials: {
        currentRevenue: "$3.2M ARR",
        projectedRevenue: "$8.5M ARR (next 12 months)",
        burnRate: "$180K/month",
        runway: "18 months"
      },
      documents: ["Business Plan", "Financial Projections", "Market Analysis", "Team Profiles"]
    };

    setTimeout(() => {
      setDeal(mockDeal);
      setLoading(false);
    }, 1000);
  }, [dealId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "trending": return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30";
      case "hot": return "bg-red-500/20 text-red-400 border-red-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading deal details...</div>
      </div>
    );
  }

  if (!deal) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Deal not found</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-emerald-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <span className="text-xl font-bold text-white">CFOx</span>
          </Link>

          <div className="flex items-center space-x-4">
            <Link href="/notifications">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white relative">
                <Bell className="w-4 h-4" />
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </Button>
            </Link>
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                <Settings className="w-4 h-4" />
              </Button>
            </Link>
            <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/investor/dashboard">
              <Button variant="outline" size="sm" className="border-slate-600 text-white">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-white">{deal.companyName}</h1>
              <p className="text-slate-400">{deal.industry} • {deal.location}</p>
            </div>
          </div>
          <Badge className={getStatusColor(deal.status)}>
            {deal.status}
          </Badge>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Company Overview */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Company Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 mb-4">{deal.detailedDescription}</p>
                <div className="flex flex-wrap gap-2">
                  {deal.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="border-slate-600 text-slate-400">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Business Model */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Business Model</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300">{deal.businessModel}</p>
              </CardContent>
            </Card>

            {/* Market & Competition */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Market & Competition</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="text-white font-semibold mb-2">Market Size</h4>
                    <p className="text-slate-300">{deal.marketSize}</p>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-2">Competition</h4>
                    <p className="text-slate-300">{deal.competition}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Team */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Users className="w-5 h-5 mr-2" />
                  Team
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 mb-2">Team Size: {deal.teamSize} members</p>
                <p className="text-slate-300">{deal.foundersInfo}</p>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Key Metrics & Actions */}
          <div className="space-y-6">
            {/* Key Metrics */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Key Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Funding Goal</span>
                    <span className="text-emerald-400 font-semibold">{deal.fundingGoal}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Financial Score</span>
                    <span className="text-white font-semibold">{deal.financialScore}/100</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Revenue</span>
                    <span className="text-white font-semibold">{deal.revenue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Employees</span>
                    <span className="text-white font-semibold">{deal.employees}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Established</span>
                    <span className="text-white font-semibold">{deal.yearEstablished}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Financial Details */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <DollarSign className="w-5 h-5 mr-2" />
                  Financials
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-slate-400 text-sm">Current Revenue</p>
                    <p className="text-white font-semibold">{deal.financials.currentRevenue}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Projected Revenue</p>
                    <p className="text-white font-semibold">{deal.financials.projectedRevenue}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Burn Rate</p>
                    <p className="text-white font-semibold">{deal.financials.burnRate}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Runway</p>
                    <p className="text-white font-semibold">{deal.financials.runway}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button className="w-full bg-emerald-600 hover:bg-emerald-700">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Contact Company
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700">
                    <Heart className="w-4 h-4 mr-2" />
                    Save to Watchlist
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700">
                    <Download className="w-4 h-4 mr-2" />
                    Download Documents
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Interest Stats */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Interest</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Views</span>
                    <span className="text-white font-semibold">{deal.viewed}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Interested</span>
                    <span className="text-white font-semibold">{deal.interested}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Theme Toggle */}
      <ThemeToggle />
    </div>
  );
}
