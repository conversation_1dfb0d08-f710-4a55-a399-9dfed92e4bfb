"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ThemeToggle from "@/components/ui/theme-toggle";
import { useAuthStore } from "@/stores/authStore";
import {
    ArrowLeft,
    Bell,
    Building,
    Calendar,
    Download,
    FileText,
    LogOut,
    Mail,
    MessageCircle,
    Phone,
    Settings,
    TrendingUp,
    User
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

interface ClientDetail {
  id: number;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  industry: string;
  location: string;
  revenue: string;
  employees: string;
  status: string;
  engagementType: string;
  startDate: string;
  nextMeeting: string;
  description: string;
  challenges: string[];
  objectives: string[];
  progress: {
    completed: number;
    total: number;
  };
  recentActivities: Array<{
    date: string;
    activity: string;
    type: string;
  }>;
  documents: string[];
}

export default function ClientDetailPage() {
  const { user } = useAuthStore();
  const params = useParams();
  const clientId = params.id as string;
  const [client, setClient] = useState<ClientDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - replace with actual API call
    const mockClient: ClientDetail = {
      id: parseInt(clientId),
      companyName: "TechStart Solutions",
      contactPerson: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "+****************",
      industry: "Technology",
      location: "San Francisco, CA",
      revenue: "$2.5M",
      employees: "25-50",
      status: "active",
      engagementType: "Financial Strategy",
      startDate: "2024-01-15",
      nextMeeting: "2024-12-22T14:00:00",
      description: "TechStart Solutions is a growing SaaS company focused on project management tools for small businesses.",
      challenges: [
        "Cash flow management",
        "Scaling operations",
        "Investment readiness",
        "Financial reporting automation"
      ],
      objectives: [
        "Improve cash flow by 30%",
        "Prepare for Series A funding",
        "Implement automated financial reporting",
        "Optimize operational costs"
      ],
      progress: {
        completed: 7,
        total: 12
      },
      recentActivities: [
        {
          date: "2024-12-18",
          activity: "Quarterly financial review completed",
          type: "meeting"
        },
        {
          date: "2024-12-15",
          activity: "Cash flow projection updated",
          type: "document"
        },
        {
          date: "2024-12-10",
          activity: "Investor pitch deck reviewed",
          type: "review"
        }
      ],
      documents: [
        "Financial Statements Q4 2024",
        "Cash Flow Projections",
        "Investor Pitch Deck",
        "Operational Cost Analysis"
      ]
    };

    setTimeout(() => {
      setClient(mockClient);
      setLoading(false);
    }, 1000);
  }, [clientId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30";
      case "pending": return "bg-amber-500/20 text-amber-400 border-amber-500/30";
      case "completed": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-white">Loading client details...</div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-white">Client not found</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-8 h-8 relative">
              <img
                src="/tenxcfo.png"
                alt="TenxCFO Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </Link>
          
          <div className="flex items-center space-x-4">
            <Link href="/notifications">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white relative">
                <Bell className="w-4 h-4" />
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </Button>
            </Link>
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                <Settings className="w-4 h-4" />
              </Button>
            </Link>
            <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/consultant/dashboard">
              <Button variant="outline" size="sm" className="border-slate-600 text-white">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-white">{client.companyName}</h1>
              <p className="text-slate-400">{client.industry} • {client.location}</p>
            </div>
          </div>
          <Badge className={getStatusColor(client.status)}>
            {client.status}
          </Badge>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Client Overview */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Client Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 mb-4">{client.description}</p>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-slate-400 text-sm">Contact Person</p>
                    <p className="text-white font-semibold">{client.contactPerson}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Engagement Type</p>
                    <p className="text-white font-semibold">{client.engagementType}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Start Date</p>
                    <p className="text-white font-semibold">{new Date(client.startDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Next Meeting</p>
                    <p className="text-white font-semibold">{new Date(client.nextMeeting).toLocaleDateString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Challenges & Objectives */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-slate-800/50 border-slate-700/50">
                <CardHeader>
                  <CardTitle className="text-white">Key Challenges</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {client.challenges.map((challenge, index) => (
                      <li key={index} className="text-slate-300 flex items-start">
                        <span className="w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        {challenge}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-slate-800/50 border-slate-700/50">
                <CardHeader>
                  <CardTitle className="text-white">Objectives</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {client.objectives.map((objective, index) => (
                      <li key={index} className="text-slate-300 flex items-start">
                        <span className="w-2 h-2 bg-emerald-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        {objective}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activities */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Recent Activities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {client.recentActivities.map((activity, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-slate-700/30 rounded-lg">
                      <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        {activity.type === 'meeting' && <Calendar className="w-4 h-4 text-blue-400" />}
                        {activity.type === 'document' && <FileText className="w-4 h-4 text-blue-400" />}
                        {activity.type === 'review' && <TrendingUp className="w-4 h-4 text-blue-400" />}
                      </div>
                      <div className="flex-1">
                        <p className="text-white font-medium">{activity.activity}</p>
                        <p className="text-slate-400 text-sm">{new Date(activity.date).toLocaleDateString()}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Key Metrics & Actions */}
          <div className="space-y-6">
            {/* Contact Information */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Contact Info
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Mail className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-300">{client.email}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-300">{client.phone}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Building className="w-4 h-4 text-slate-400" />
                    <span className="text-slate-300">{client.location}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Company Metrics */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Company Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Revenue</span>
                    <span className="text-emerald-400 font-semibold">{client.revenue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Employees</span>
                    <span className="text-white font-semibold">{client.employees}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Industry</span>
                    <span className="text-white font-semibold">{client.industry}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Progress */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Engagement Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Completed</span>
                    <span className="text-white font-semibold">{client.progress.completed}/{client.progress.total}</span>
                  </div>
                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div 
                      className="bg-emerald-500 h-2 rounded-full" 
                      style={{ width: `${(client.progress.completed / client.progress.total) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-slate-400 text-sm">
                    {Math.round((client.progress.completed / client.progress.total) * 100)}% Complete
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button className="w-full bg-amber-600 hover:bg-amber-700">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Send Message
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule Meeting
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700">
                    <Download className="w-4 h-4 mr-2" />
                    Download Reports
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      
      {/* Theme Toggle */}
      <ThemeToggle />
    </div>
  );
}
