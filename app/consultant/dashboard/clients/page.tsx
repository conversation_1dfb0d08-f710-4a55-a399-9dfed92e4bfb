"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import ThemeToggle from "@/components/ui/theme-toggle";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    Bell,
    Building,
    Calendar,
    DollarSign,
    Filter,
    LogOut,
    Mail,
    Phone,
    Search,
    Settings,
    TrendingUp,
    Users
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface Client {
  id: number;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  industry: string;
  revenue: string;
  status: string;
  engagementType: string;
  nextMeeting: string;
  progress: number;
}

export default function ClientsPage() {
  const { user } = useAuthStore();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  // Mock data - replace with actual API call
  const clients: Client[] = [
    {
      id: 1,
      companyName: "TechStart Solutions",
      contactPerson: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      industry: "Technology",
      revenue: "$2.5M",
      status: "active",
      engagementType: "Financial Strategy",
      nextMeeting: "2024-12-22T14:00:00",
      progress: 58
    },
    {
      id: 2,
      companyName: "GreenEnergy Corp",
      contactPerson: "Michael Chen",
      email: "<EMAIL>",
      phone: "+****************",
      industry: "Energy",
      revenue: "$5.2M",
      status: "active",
      engagementType: "Investment Readiness",
      nextMeeting: "2024-12-25T10:00:00",
      progress: 75
    },
    {
      id: 3,
      companyName: "HealthTech Innovations",
      contactPerson: "Emily Rodriguez",
      email: "<EMAIL>",
      phone: "+****************",
      industry: "Healthcare",
      revenue: "$1.8M",
      status: "pending",
      engagementType: "Financial Planning",
      nextMeeting: "2024-12-28T15:30:00",
      progress: 25
    },
    {
      id: 4,
      companyName: "RetailMax Solutions",
      contactPerson: "David Wilson",
      email: "<EMAIL>",
      phone: "+****************",
      industry: "Retail",
      revenue: "$3.1M",
      status: "completed",
      engagementType: "Cost Optimization",
      nextMeeting: "",
      progress: 100
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30";
      case "pending": return "bg-amber-500/20 text-amber-400 border-amber-500/30";
      case "completed": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.industry.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || client.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <span className="text-xl font-bold text-white">CFOx</span>
          </Link>
          
          <div className="flex items-center space-x-4">
            <Link href="/notifications">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white relative">
                <Bell className="w-4 h-4" />
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </Button>
            </Link>
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                <Settings className="w-4 h-4" />
              </Button>
            </Link>
            <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">My Clients</h1>
              <p className="text-slate-400">Manage your client engagements and track progress</p>
            </div>
            <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30">
              {filteredClients.length} Active Clients
            </Badge>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-800/50 border-slate-700/50 text-white placeholder:text-slate-400"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={statusFilter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("all")}
                className={statusFilter === "all" ? "bg-amber-600 hover:bg-amber-700" : "border-slate-600 text-white"}
              >
                All
              </Button>
              <Button
                variant={statusFilter === "active" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("active")}
                className={statusFilter === "active" ? "bg-amber-600 hover:bg-amber-700" : "border-slate-600 text-white"}
              >
                Active
              </Button>
              <Button
                variant={statusFilter === "pending" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("pending")}
                className={statusFilter === "pending" ? "bg-amber-600 hover:bg-amber-700" : "border-slate-600 text-white"}
              >
                Pending
              </Button>
              <Button
                variant={statusFilter === "completed" ? "default" : "outline"}
                size="sm"
                onClick={() => setStatusFilter("completed")}
                className={statusFilter === "completed" ? "bg-amber-600 hover:bg-amber-700" : "border-slate-600 text-white"}
              >
                Completed
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Clients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClients.map((client, index) => (
            <motion.div
              key={client.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Link href={`/consultant/dashboard/client/${client.id}`}>
                <Card className="bg-slate-800/50 border-slate-700/50 hover:bg-slate-800/70 transition-all duration-300 cursor-pointer">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-amber-500/20 rounded-lg flex items-center justify-center">
                          <Building className="w-6 h-6 text-amber-400" />
                        </div>
                        <div>
                          <CardTitle className="text-white text-lg">{client.companyName}</CardTitle>
                          <p className="text-slate-400 text-sm">{client.contactPerson}</p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(client.status)}>
                        {client.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-400">Industry</span>
                        <span className="text-white">{client.industry}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-400">Revenue</span>
                        <span className="text-emerald-400 font-semibold">{client.revenue}</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-400">Engagement</span>
                        <span className="text-white">{client.engagementType}</span>
                      </div>
                      
                      {/* Progress Bar */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-slate-400">Progress</span>
                          <span className="text-white">{client.progress}%</span>
                        </div>
                        <div className="w-full bg-slate-700 rounded-full h-2">
                          <div 
                            className="bg-amber-500 h-2 rounded-full transition-all duration-300" 
                            style={{ width: `${client.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Next Meeting */}
                      {client.nextMeeting && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Calendar className="w-4 h-4 text-slate-400" />
                          <span className="text-slate-400">Next meeting:</span>
                          <span className="text-white">
                            {new Date(client.nextMeeting).toLocaleDateString()}
                          </span>
                        </div>
                      )}

                      {/* Contact Info */}
                      <div className="pt-3 border-t border-slate-700/50 space-y-2">
                        <div className="flex items-center space-x-2 text-sm">
                          <Mail className="w-4 h-4 text-slate-400" />
                          <span className="text-slate-300 truncate">{client.email}</span>
                        </div>
                        <div className="flex items-center space-x-2 text-sm">
                          <Phone className="w-4 h-4 text-slate-400" />
                          <span className="text-slate-300">{client.phone}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredClients.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <Users className="w-16 h-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No clients found</h3>
            <p className="text-slate-400">Try adjusting your search or filter criteria</p>
          </motion.div>
        )}
      </div>
      
      {/* Theme Toggle */}
      <ThemeToggle />
    </div>
  );
}
