"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    Award,
    Bell,
    Calendar,
    DollarSign,
    LogOut,
    Mail,
    Phone,
    Settings,
    TrendingUp,
    Users
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function ConsultantDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.push('/');
  };
  // Mock data
  const stats = {
    totalClients: 24,
    activeClients: 18,
    totalEarnings: "$3.6K",
    monthlyEarnings: "$585",
    successRate: 85,
    avgImprovement: 23
  };

  const clients = [
    {
      id: 1,
      name: "TechStart Solutions",
      industry: "Technology",
      currentScore: 68,
      initialScore: 45,
      improvement: 23,
      status: "active",
      lastContact: "2 days ago",
      nextMilestone: "Upload Q4 financials"
    },
    {
      id: 2,
      name: "GreenManufacturing Ltd",
      industry: "Manufacturing",
      currentScore: 78,
      initialScore: 62,
      improvement: 16,
      status: "completed",
      lastContact: "1 week ago",
      nextMilestone: "Investor presentation ready"
    },
    {
      id: 3,
      name: "HealthCare Innovations",
      industry: "Healthcare",
      currentScore: 55,
      initialScore: 42,
      improvement: 13,
      status: "needs_attention",
      lastContact: "5 days ago",
      nextMilestone: "Complete compliance documents"
    }
  ];

  const recentEarnings = [
    { id: 1, client: "TechStart Solutions", amount: "$325", type: "Score Improvement Bonus", date: "2 days ago" },
    { id: 2, client: "GreenManufacturing Ltd", amount: "$325", type: "Successful Referral", date: "1 week ago" },
    { id: 3, client: "RetailChain Pvt Ltd", amount: "$65", type: "Monthly Retainer", date: "2 weeks ago" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30";
      case "completed": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "needs_attention": return "bg-amber-500/20 text-amber-400 border-amber-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-20 h-20 relative">
              <img
                src="/tenxcfo-dark.png"
                alt="TenxCFO Logo"
                width={80}
                height={80}
                className="w-full h-full object-contain logo-theme-aware"
              />
            </div>
          </Link>
          
          <div className="flex items-center space-x-4">
            <Link href="/notifications">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white relative">
                <Bell className="w-4 h-4" />
                <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </Button>
            </Link>
            <Link href="/settings">
              <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white">
                <Settings className="w-4 h-4" />
              </Button>
            </Link>
            <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white" onClick={handleLogout}>
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Welcome back, {user?.name || 'Consultant'}</h1>
              <p className="text-slate-400">Manage your clients and track your consulting performance</p>
            </div>
            <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30">
              Certified Consultant
            </Badge>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-amber-500/20 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-amber-400" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">{stats.totalClients}</div>
                <p className="text-slate-400 text-sm">Total Clients</p>
                <p className="text-emerald-400 text-xs mt-1">{stats.activeClients} active</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-emerald-500/20 rounded-lg flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-emerald-400" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">{stats.totalEarnings}</div>
                <p className="text-slate-400 text-sm">Total Earnings</p>
                <p className="text-emerald-400 text-xs mt-1">{stats.monthlyEarnings} this month</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-blue-400" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">{stats.successRate}%</div>
                <p className="text-slate-400 text-sm">Success Rate</p>
                <div className="mt-2">
                  <Progress value={stats.successRate} className="h-1" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <Award className="w-6 h-6 text-purple-400" />
                  </div>
                </div>
                <div className="text-2xl font-bold text-white mb-1">+{stats.avgImprovement}</div>
                <p className="text-slate-400 text-sm">Avg Score Improvement</p>
                <p className="text-purple-400 text-xs mt-1">points per client</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Client Management */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="lg:col-span-2"
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-white flex items-center">
                      <Users className="w-5 h-5 mr-2 text-amber-400" />
                      Active Clients
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Monitor your clients' progress and next steps
                    </CardDescription>
                  </div>
                  <Button size="sm" className="bg-amber-600 hover:bg-amber-700">
                    Add Client
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {clients.map((client) => (
                    <div
                      key={client.id}
                      className="p-6 bg-slate-700/30 rounded-lg border border-slate-600/30"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-bold text-white">{client.name}</h3>
                            <Badge className={getStatusColor(client.status)}>
                              {client.status.replace('_', ' ')}
                            </Badge>
                          </div>
                          <p className="text-slate-400 text-sm mb-2">{client.industry}</p>
                          <p className="text-slate-300 text-sm">{client.nextMilestone}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-emerald-400 mb-1">{client.currentScore}</div>
                          <p className="text-slate-400 text-xs">Current Score</p>
                          <p className="text-emerald-400 text-xs">+{client.improvement} improvement</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-slate-400 text-sm">
                          <span className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            Last contact: {client.lastContact}
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                            <Phone className="w-4 h-4 mr-2" />
                            Call
                          </Button>
                          <Button variant="outline" size="sm" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                            <Mail className="w-4 h-4 mr-2" />
                            Email
                          </Button>
                          <Link href={`/consultant/dashboard/client/${client.id}`}>
                            <Button size="sm" className="bg-amber-600 hover:bg-amber-700">
                              View Details
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Earnings */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <DollarSign className="w-5 h-5 mr-2 text-emerald-400" />
                  Recent Earnings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentEarnings.map((earning) => (
                    <div key={earning.id} className="flex items-start justify-between p-4 bg-slate-700/30 rounded-lg">
                      <div className="flex-1">
                        <p className="text-white font-medium">{earning.amount}</p>
                        <p className="text-slate-400 text-sm">{earning.type}</p>
                        <p className="text-slate-500 text-xs">{earning.client}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-slate-400 text-xs">{earning.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-slate-800/50 border-slate-700/50 mt-6">
              <CardHeader>
                <CardTitle className="text-white text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href="/consultant/dashboard/clients">
                    <Button className="w-full bg-amber-600 hover:bg-amber-700 justify-start">
                      <Users className="w-4 h-4 mr-2" />
                      View All Clients
                    </Button>
                  </Link>
                  <Link href="/consultant/clients">
                    <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700 justify-start">
                      <Users className="w-4 h-4 mr-2" />
                      Find New Clients
                    </Button>
                  </Link>
                  <Link href="/consultant/dashboard/schedule">
                    <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700 justify-start">
                      <Calendar className="w-4 h-4 mr-2" />
                      Schedule Calls
                    </Button>
                  </Link>
                  <Link href="/consultant/dashboard/analytics">
                    <Button variant="outline" className="w-full border-slate-600 text-white hover:bg-slate-700 justify-start">
                      <TrendingUp className="w-4 h-4 mr-2" />
                      View Analytics
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
