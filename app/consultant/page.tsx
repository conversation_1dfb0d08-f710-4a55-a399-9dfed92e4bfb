"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import ThemeToggle from "@/components/ui/theme-toggle";
import { motion } from "framer-motion";
import { ArrowRight, Award, BarChart3, DollarSign, Target, TrendingUp, Users } from "lucide-react";
import Link from "next/link";

export default function ConsultantOverview() {
  const benefits = [
    {
      icon: DollarSign,
      title: "Earn Referral Rewards",
      description: "Get paid for every successful SME you help improve and connect with investors.",
      color: "text-emerald-400",
      bgColor: "bg-emerald-500/20",
    },
    {
      icon: Users,
      title: "Client Management Tools",
      description: "Manage your SME clients with comprehensive dashboards and progress tracking.",
      color: "text-blue-400",
      bgColor: "bg-blue-500/20",
    },
    {
      icon: BarChart3,
      title: "Performance Analytics",
      description: "Track your success rate, earnings, and client improvement metrics.",
      color: "text-purple-400",
      bgColor: "bg-purple-500/20",
    },
    {
      icon: Award,
      title: "Expert Certification",
      description: "Get certified as a CFOx consultant and build your professional credibility.",
      color: "text-amber-400",
      bgColor: "bg-amber-500/20",
    },
    {
      icon: Target,
      title: "Lead Generation",
      description: "Access our network of SMEs looking for financial consulting services.",
      color: "text-red-400",
      bgColor: "bg-red-500/20",
    },
    {
      icon: TrendingUp,
      title: "Growth Opportunities",
      description: "Scale your consulting business with our platform and resources.",
      color: "text-cyan-400",
      bgColor: "bg-cyan-500/20",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-8 h-8 relative">
              <img
                src="/tenxcfo.png"
                alt="TenxCFO Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </Link>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/sme" className="text-slate-300 hover:text-white transition-colors">For SMEs</Link>
            <Link href="/investor" className="text-slate-300 hover:text-white transition-colors">For Investors</Link>
            <Link href="/consultant" className="text-white font-semibold border-b-2 border-amber-400">For Consultants</Link>
            <Link href="/10x-growth-hack" className="text-slate-300 hover:text-white transition-colors">10X Growth</Link>
            <Link href="/pricing" className="text-slate-300 hover:text-white transition-colors">Pricing</Link>
            <Link href="/auth/signin" className="text-slate-300 hover:text-white transition-colors">Log in</Link>
            <Link href="/consultant/signup">
              <Button className="bg-amber-600 hover:bg-amber-700 text-white rounded-full px-6">
                Start Consulting
              </Button>
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-6 px-6 py-2 text-sm font-medium bg-amber-500/10 text-amber-300 border-amber-500/20">
              🎯 For Consultants
            </Badge>
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-8 leading-tight">
              Help SMEs Grow &
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-amber-400 to-orange-400">
                Earn Rewards
              </span>
            </h1>
            <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Join our network of certified consultants helping SMEs improve their financial health, 
              connect with investors, and achieve sustainable growth.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/consultant/signup">
                <Button size="lg" className="px-8 py-4 text-lg bg-amber-600 hover:bg-amber-700 text-white rounded-full">
                  Join as Consultant <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg border-white/30 text-white hover:bg-white/10 rounded-full">
                  View Opportunities
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-slate-800/30">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">Why Partner with CFOx?</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              Our platform provides everything you need to build a successful consulting practice 
              while helping SMEs achieve their growth goals.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-amber-500/50 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className={`w-16 h-16 ${benefit.bgColor} rounded-2xl flex items-center justify-center mb-6`}>
                      <benefit.icon className={`w-8 h-8 ${benefit.color}`} />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{benefit.title}</h3>
                    <p className="text-slate-300">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Earning Potential */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">Earning Potential</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              Multiple revenue streams to maximize your consulting income.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-emerald-400 mb-2">$325</div>
                  <div className="text-slate-300 mb-4">Per Successful SME Referral</div>
                  <p className="text-slate-400 text-sm">
                    Earn when SMEs you help get funded through our platform
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-blue-400 mb-2">$65</div>
                  <div className="text-slate-300 mb-4">Per Financial Health Improvement</div>
                  <p className="text-slate-400 text-sm">
                    Bonus for helping SMEs improve their CFOx score by 20+ points
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 text-center">
                <CardContent className="p-8">
                  <div className="text-4xl font-bold text-amber-400 mb-2">$2.6K+</div>
                  <div className="text-slate-300 mb-4">Monthly Potential</div>
                  <p className="text-slate-400 text-sm">
                    Top consultants earn $2.6K+ per month through our platform
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-amber-600 to-orange-600">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🚀 Join Our Network
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">Ready to Start Consulting?</h2>
            <p className="text-xl text-amber-100 mb-8 max-w-2xl mx-auto">
              Join hundreds of consultants who are already helping SMEs grow and earning rewards through CFOx.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/consultant/signup">
                <Button size="lg" className="bg-white text-amber-600 hover:bg-amber-50 text-lg px-8 py-4 rounded-full">
                  Apply as Consultant <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link href="/pricing">
                <Button variant="outline" size="lg" className="border-white/30 text-white hover:bg-white/10 text-lg px-8 py-4 rounded-full">
                  View Commission Structure
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-800 py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">CF</span>
                </div>
                <span className="text-xl font-bold text-white">CFOx</span>
              </div>
              <p className="text-slate-400 mb-4">
                Connecting SMEs with smart investors through data-driven insights and automated financial scoring.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Platform</h4>
              <ul className="space-y-2 text-slate-400">
                <li><Link href="/sme" className="hover:text-white transition-colors">For SMEs</Link></li>
                <li><Link href="/investor" className="hover:text-white transition-colors">For Investors</Link></li>
                <li><Link href="/consultant" className="hover:text-white transition-colors">For Consultants</Link></li>
                <li><Link href="/10x-growth-hack" className="hover:text-white transition-colors">10X Growth</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Resources</h4>
              <ul className="space-y-2 text-slate-400">
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Case Studies</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">&copy; 2024 CFOx. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Terms of Service</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>

      {/* Theme Toggle */}
      <ThemeToggle />
    </div>
  );
}
