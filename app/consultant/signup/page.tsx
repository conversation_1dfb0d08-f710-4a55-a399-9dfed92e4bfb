"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight, CheckCircle } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function ConsultantSignup() {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;
  
  const [formData, setFormData] = useState({
    // Personal Info
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    
    // Professional Info
    experience: "",
    expertise: [] as string[],
    certifications: "",
    linkedinProfile: "",
    currentRole: "",
    organization: "",
    
    // Consulting Preferences
    consultingAreas: [] as string[],
    availability: "",
    hourlyRate: "",
    preferredIndustries: [] as string[],
    bio: ""
  });

  const updateFormData = (field: string, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    // Handle form submission
    console.log("Consultant signup data:", formData);
    alert("Thank you for signing up! We'll review your application and get back to you within 24 hours.");
  };

  const expertiseOptions = [
    "Financial Planning", "Business Strategy", "Marketing", "Operations", 
    "Technology", "Legal", "HR", "Sales", "Product Development", "International Expansion"
  ];

  const consultingAreaOptions = [
    "Business Plan Development", "Financial Analysis", "Market Research", 
    "Operational Efficiency", "Digital Transformation", "Compliance", 
    "Fundraising Strategy", "Growth Planning"
  ];

  const industryOptions = [
    "Technology", "Healthcare", "Manufacturing", "Retail", "Finance", 
    "Education", "Real Estate", "Food & Beverage", "Energy", "Transportation"
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/consultant" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <span className="text-xl font-bold text-white">CFOx</span>
          </Link>
          <Badge className="bg-amber-500/20 text-amber-400 border-amber-500/30">
            Consultant Registration
          </Badge>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Progress Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-white">Consultant Registration</h1>
            <span className="text-slate-400">Step {currentStep} of {totalSteps}</span>
          </div>
          <Progress value={(currentStep / totalSteps) * 100} className="h-2 mb-2" />
          <p className="text-slate-400 text-sm">Complete your profile to get started with CFOx</p>
        </motion.div>

        {/* Form */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-2xl text-white">
                {currentStep === 1 && "Personal Information"}
                {currentStep === 2 && "Professional Background"}
                {currentStep === 3 && "Consulting Preferences"}
              </CardTitle>
              <CardDescription className="text-slate-400">
                {currentStep === 1 && "Tell us about yourself"}
                {currentStep === 2 && "Share your professional experience and expertise"}
                {currentStep === 3 && "Define your consulting preferences and availability"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Step 1: Personal Information */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-slate-300">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData("firstName", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-slate-300">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData("lastName", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="Doe"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-slate-300">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => updateFormData("email", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <Label htmlFor="phone" className="text-slate-300">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => updateFormData("phone", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="+****************"
                    />
                  </div>
                </div>
              )}

              {/* Step 2: Professional Background */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="experience" className="text-slate-300">Years of Experience *</Label>
                    <select
                      id="experience"
                      value={formData.experience}
                      onChange={(e) => updateFormData("experience", e.target.value)}
                      className="w-full mt-1 bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select experience level</option>
                      <option value="1-3">1-3 years</option>
                      <option value="3-5">3-5 years</option>
                      <option value="5-10">5-10 years</option>
                      <option value="10-15">10-15 years</option>
                      <option value="15+">15+ years</option>
                    </select>
                  </div>

                  <div>
                    <Label className="text-slate-300">Areas of Expertise *</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {expertiseOptions.map((option) => (
                        <label key={option} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.expertise.includes(option)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                updateFormData("expertise", [...formData.expertise, option]);
                              } else {
                                updateFormData("expertise", formData.expertise.filter(item => item !== option));
                              }
                            }}
                            className="rounded border-slate-600 bg-slate-700/50 text-amber-600"
                          />
                          <span className="text-slate-300 text-sm">{option}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="currentRole" className="text-slate-300">Current Role/Position</Label>
                    <Input
                      id="currentRole"
                      value={formData.currentRole}
                      onChange={(e) => updateFormData("currentRole", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="Senior Business Analyst"
                    />
                  </div>

                  <div>
                    <Label htmlFor="organization" className="text-slate-300">Current Organization</Label>
                    <Input
                      id="organization"
                      value={formData.organization}
                      onChange={(e) => updateFormData("organization", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="Company Name or Independent"
                    />
                  </div>

                  <div>
                    <Label htmlFor="linkedinProfile" className="text-slate-300">LinkedIn Profile</Label>
                    <Input
                      id="linkedinProfile"
                      value={formData.linkedinProfile}
                      onChange={(e) => updateFormData("linkedinProfile", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="https://linkedin.com/in/yourprofile"
                    />
                  </div>
                </div>
              )}

              {/* Step 3: Consulting Preferences */}
              {currentStep === 3 && (
                <div className="space-y-4">
                  <div>
                    <Label className="text-slate-300">Consulting Areas *</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {consultingAreaOptions.map((option) => (
                        <label key={option} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={formData.consultingAreas.includes(option)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                updateFormData("consultingAreas", [...formData.consultingAreas, option]);
                              } else {
                                updateFormData("consultingAreas", formData.consultingAreas.filter(item => item !== option));
                              }
                            }}
                            className="rounded border-slate-600 bg-slate-700/50 text-amber-600"
                          />
                          <span className="text-slate-300 text-sm">{option}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="hourlyRate" className="text-slate-300">Preferred Hourly Rate (USD)</Label>
                    <select
                      id="hourlyRate"
                      value={formData.hourlyRate}
                      onChange={(e) => updateFormData("hourlyRate", e.target.value)}
                      className="w-full mt-1 bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select rate range</option>
                      <option value="25-50">$25 - $50/hour</option>
                      <option value="50-100">$50 - $100/hour</option>
                      <option value="100-200">$100 - $200/hour</option>
                      <option value="200+">$200+/hour</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="availability" className="text-slate-300">Availability</Label>
                    <select
                      id="availability"
                      value={formData.availability}
                      onChange={(e) => updateFormData("availability", e.target.value)}
                      className="w-full mt-1 bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select availability</option>
                      <option value="part-time">Part-time (10-20 hours/week)</option>
                      <option value="full-time">Full-time (40+ hours/week)</option>
                      <option value="project-based">Project-based</option>
                      <option value="flexible">Flexible</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="bio" className="text-slate-300">Professional Bio</Label>
                    <Textarea
                      id="bio"
                      value={formData.bio}
                      onChange={(e) => updateFormData("bio", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="Brief description of your background and what you can offer to SMEs..."
                      rows={4}
                    />
                  </div>
                </div>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between pt-6">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                  className="border-slate-600 text-white hover:bg-slate-700"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Previous
                </Button>

                {currentStep < totalSteps ? (
                  <Button
                    onClick={nextStep}
                    className="bg-amber-600 hover:bg-amber-700 text-white"
                  >
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    className="bg-amber-600 hover:bg-amber-700 text-white"
                  >
                    Submit Application
                    <CheckCircle className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
