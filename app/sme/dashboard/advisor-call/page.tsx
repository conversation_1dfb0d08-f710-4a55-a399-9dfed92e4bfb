"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowLeft,
    Calendar,
    CheckCircle,
    Phone,
    Star,
    User,
    Video
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function AdvisorCallPage() {
  const { user } = useAuthStore();
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>("");
  const [callType, setCallType] = useState<"phone" | "video">("video");

  const advisors = [
    {
      id: 1,
      name: "<PERSON>",
      title: "Senior Business Advisor",
      expertise: ["Financial Planning", "Growth Strategy", "Investor Relations"],
      rating: 4.9,
      reviews: 127,
      experience: "15+ years",
      image: "/api/placeholder/64/64"
    },
    {
      id: 2,
      name: "<PERSON>",
      title: "Investment Specialist",
      expertise: ["Fundraising", "Valuation", "Market Analysis"],
      rating: 4.8,
      reviews: 89,
      experience: "12+ years",
      image: "/api/placeholder/64/64"
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      title: "Operations Consultant",
      expertise: ["Process Optimization", "Digital Transformation", "Scaling"],
      rating: 4.9,
      reviews: 156,
      experience: "18+ years",
      image: "/api/placeholder/64/64"
    }
  ];

  const timeSlots = [
    "9:00 AM", "10:00 AM", "11:00 AM", "2:00 PM", "3:00 PM", "4:00 PM"
  ];

  const upcomingCalls = [
    {
      advisor: "Sarah Johnson",
      date: "Tomorrow",
      time: "2:00 PM",
      type: "video",
      topic: "Q4 Financial Review"
    },
    {
      advisor: "Michael Chen",
      date: "Dec 20",
      time: "10:00 AM",
      type: "phone",
      topic: "Investor Pitch Preparation"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/sme/dashboard" className="flex items-center space-x-3">
              <ArrowLeft className="w-5 h-5 text-slate-400 hover:text-white transition-colors" />
              <span className="text-slate-400 hover:text-white transition-colors">Back to Dashboard</span>
            </Link>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 relative">
              <img
                src="/tenxcfo.png"
                alt="TenxCFO Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Schedule Advisor Call</h1>
              <p className="text-slate-400">Get expert guidance from our certified business advisors</p>
            </div>
            <Badge className="bg-emerald-500/20 text-emerald-400 border-emerald-500/30">
              Premium Feature
            </Badge>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Scheduling Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Schedule New Call
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Book a consultation with one of our expert advisors
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Advisor Selection */}
                <div>
                  <Label className="text-white mb-3 block">Select Advisor</Label>
                  <div className="space-y-3">
                    {advisors.map((advisor) => (
                      <div key={advisor.id} className="p-4 border border-slate-600 rounded-lg hover:border-blue-500 transition-colors cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-slate-600 rounded-full flex items-center justify-center mr-4">
                              <User className="w-6 h-6 text-slate-300" />
                            </div>
                            <div>
                              <h3 className="text-white font-semibold">{advisor.name}</h3>
                              <p className="text-slate-400 text-sm">{advisor.title}</p>
                              <div className="flex items-center mt-1">
                                <Star className="w-4 h-4 text-amber-400 mr-1" />
                                <span className="text-slate-300 text-sm">{advisor.rating} ({advisor.reviews} reviews)</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30 mb-2">
                              {advisor.experience}
                            </Badge>
                            <div className="flex flex-wrap gap-1">
                              {advisor.expertise.slice(0, 2).map((skill, index) => (
                                <Badge key={index} variant="outline" className="text-xs border-slate-600 text-slate-400">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Date Selection */}
                <div>
                  <Label className="text-white mb-3 block">Select Date</Label>
                  <Input
                    type="date"
                    className="bg-slate-700 border-slate-600 text-white"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>

                {/* Time Selection */}
                <div>
                  <Label className="text-white mb-3 block">Select Time</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {timeSlots.map((time) => (
                      <Button
                        key={time}
                        variant={selectedTimeSlot === time ? "default" : "outline"}
                        className={`${
                          selectedTimeSlot === time 
                            ? "bg-blue-600 hover:bg-blue-700" 
                            : "border-slate-600 text-slate-300 hover:bg-slate-700"
                        }`}
                        onClick={() => setSelectedTimeSlot(time)}
                      >
                        {time}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Call Type */}
                <div>
                  <Label className="text-white mb-3 block">Call Type</Label>
                  <div className="flex space-x-4">
                    <Button
                      variant={callType === "video" ? "default" : "outline"}
                      className={`flex-1 ${
                        callType === "video" 
                          ? "bg-blue-600 hover:bg-blue-700" 
                          : "border-slate-600 text-slate-300 hover:bg-slate-700"
                      }`}
                      onClick={() => setCallType("video")}
                    >
                      <Video className="w-4 h-4 mr-2" />
                      Video Call
                    </Button>
                    <Button
                      variant={callType === "phone" ? "default" : "outline"}
                      className={`flex-1 ${
                        callType === "phone" 
                          ? "bg-blue-600 hover:bg-blue-700" 
                          : "border-slate-600 text-slate-300 hover:bg-slate-700"
                      }`}
                      onClick={() => setCallType("phone")}
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Phone Call
                    </Button>
                  </div>
                </div>

                {/* Topic */}
                <div>
                  <Label className="text-white mb-3 block">Discussion Topic</Label>
                  <Textarea
                    placeholder="What would you like to discuss? (e.g., fundraising strategy, financial planning, growth opportunities)"
                    className="bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                    rows={3}
                  />
                </div>

                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule Call
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Upcoming Calls & Info */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            {/* Upcoming Calls */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Upcoming Calls</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingCalls.map((call, index) => (
                    <div key={index} className="p-3 bg-slate-700/50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-white font-semibold">{call.advisor}</span>
                        {call.type === 'video' ? (
                          <Video className="w-4 h-4 text-blue-400" />
                        ) : (
                          <Phone className="w-4 h-4 text-emerald-400" />
                        )}
                      </div>
                      <p className="text-slate-400 text-sm">{call.topic}</p>
                      <div className="flex items-center text-slate-300 text-sm mt-2">
                        <Calendar className="w-4 h-4 mr-1" />
                        {call.date} at {call.time}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Why Schedule a Call?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-emerald-400 mr-2 mt-0.5" />
                    <div>
                      <p className="text-white text-sm font-semibold">Expert Guidance</p>
                      <p className="text-slate-400 text-xs">Get personalized advice from certified advisors</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-emerald-400 mr-2 mt-0.5" />
                    <div>
                      <p className="text-white text-sm font-semibold">Strategic Planning</p>
                      <p className="text-slate-400 text-xs">Develop actionable growth strategies</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-emerald-400 mr-2 mt-0.5" />
                    <div>
                      <p className="text-white text-sm font-semibold">Investor Readiness</p>
                      <p className="text-slate-400 text-xs">Prepare for funding rounds and pitches</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
