"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import {
    AlertCircle,
    ArrowLeft,
    CheckCircle,
    Clock,
    Download,
    Eye,
    FileText,
    Upload,
    X
} from "lucide-react";
import Link from "next/link";
import { useCallback, useState } from "react";

export default function SMEUpload() {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([
    { id: 1, name: "Financial_Statements_2023.pdf", size: "2.4 MB", status: "completed", uploadDate: "2024-01-15" },
    { id: 2, name: "Tax_Returns_2023.pdf", size: "1.8 MB", status: "completed", uploadDate: "2024-01-14" },
    { id: 3, name: "Bank_Statements_Q4.pdf", size: "3.2 MB", status: "processing", uploadDate: "2024-01-16" },
  ]);

  const requiredDocuments = [
    { name: "Financial Statements (Last 3 years)", required: true, uploaded: true },
    { name: "Tax Returns (Last 3 years)", required: true, uploaded: true },
    { name: "Bank Statements (Last 12 months)", required: true, uploaded: false },
    { name: "Business Registration Certificate", required: true, uploaded: false },
    { name: "GST Returns (Last 12 months)", required: false, uploaded: false },
    { name: "Audit Reports (If available)", required: false, uploaded: false },
  ];

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      // Handle file upload logic here
      console.log("Files dropped:", e.dataTransfer.files);
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      // Handle file upload logic here
      console.log("Files selected:", e.target.files);
    }
  };

  const completedDocs = requiredDocuments.filter(doc => doc.uploaded).length;
  const totalRequired = requiredDocuments.filter(doc => doc.required).length;
  const completionPercentage = (completedDocs / requiredDocuments.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/sme/dashboard" className="flex items-center space-x-3">
              <ArrowLeft className="w-5 h-5 text-slate-400 hover:text-white" />
              <div className="w-8 h-8 relative">
                <img
                  src="/tenxcfo.png"
                  alt="TenxCFO Logo"
                  className="w-full h-full object-contain"
                />
              </div>
              <span className="text-xl font-bold text-white">TenxCFO</span>
            </Link>
          </div>
          <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
            Document Upload
          </Badge>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-3xl font-bold text-white mb-2">Document Upload</h1>
          <p className="text-slate-400 mb-4">Upload your business documents for financial health analysis</p>
          
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-slate-300">Upload Progress</span>
                <span className="text-sm text-slate-400">{completedDocs}/{requiredDocuments.length} documents</span>
              </div>
              <Progress value={completionPercentage} className="h-2" />
            </div>
            <Badge className={`${completionPercentage === 100 ? 'bg-emerald-500/20 text-emerald-400' : 'bg-amber-500/20 text-amber-400'}`}>
              {Math.round(completionPercentage)}% Complete
            </Badge>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Upload Area */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="lg:col-span-2"
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Upload className="w-5 h-5 mr-2 text-blue-400" />
                  Upload Documents
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Drag and drop files or click to browse. Supported formats: PDF, JPG, PNG, Excel
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Drag and Drop Area */}
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    dragActive 
                      ? "border-blue-500 bg-blue-500/10" 
                      : "border-slate-600 hover:border-slate-500"
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <Upload className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">
                    Drop files here or click to upload
                  </h3>
                  <p className="text-slate-400 mb-4">
                    Maximum file size: 10MB per file
                  </p>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,.jpg,.jpeg,.png,.xlsx,.xls"
                    onChange={handleFileInput}
                    className="hidden"
                    id="file-upload"
                  />
                  <label htmlFor="file-upload">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      Browse Files
                    </Button>
                  </label>
                </div>

                {/* Uploaded Files List */}
                {uploadedFiles.length > 0 && (
                  <div className="mt-6">
                    <h4 className="text-white font-semibold mb-4">Recently Uploaded</h4>
                    <div className="space-y-3">
                      {uploadedFiles.map((file) => (
                        <div
                          key={file.id}
                          className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-slate-600/30"
                        >
                          <div className="flex items-center space-x-3">
                            <FileText className="w-8 h-8 text-blue-400" />
                            <div>
                              <p className="text-white font-medium">{file.name}</p>
                              <p className="text-slate-400 text-sm">{file.size} • {file.uploadDate}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {file.status === "completed" ? (
                              <CheckCircle className="w-5 h-5 text-emerald-400" />
                            ) : file.status === "processing" ? (
                              <Clock className="w-5 h-5 text-amber-400" />
                            ) : (
                              <AlertCircle className="w-5 h-5 text-red-400" />
                            )}
                            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-white">
                              <Download className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-slate-400 hover:text-red-400">
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Required Documents Checklist */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <CheckCircle className="w-5 h-5 mr-2 text-emerald-400" />
                  Document Checklist
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Required documents for analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {requiredDocuments.map((doc, index) => (
                    <div
                      key={index}
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        doc.uploaded 
                          ? "bg-emerald-500/10 border border-emerald-500/30" 
                          : doc.required 
                          ? "bg-slate-700/30 border border-slate-600/30" 
                          : "bg-slate-700/20 border border-slate-600/20"
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        {doc.uploaded ? (
                          <CheckCircle className="w-5 h-5 text-emerald-400" />
                        ) : (
                          <div className={`w-5 h-5 rounded-full border-2 ${
                            doc.required ? "border-amber-400" : "border-slate-500"
                          }`} />
                        )}
                        <div>
                          <p className={`font-medium ${doc.uploaded ? "text-emerald-400" : "text-white"}`}>
                            {doc.name}
                          </p>
                          <p className="text-xs text-slate-400">
                            {doc.required ? "Required" : "Optional"}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5" />
                    <div>
                      <p className="text-blue-400 font-medium text-sm">Upload Tips</p>
                      <ul className="text-slate-300 text-xs mt-1 space-y-1">
                        <li>• Ensure documents are clear and readable</li>
                        <li>• Use PDF format when possible</li>
                        <li>• Include all pages of multi-page documents</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-8 flex justify-between"
        >
          <Link href="/sme/dashboard">
            <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
          </Link>
          
          <div className="flex space-x-4">
            <Button 
              variant="outline" 
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
              disabled={completedDocs < totalRequired}
            >
              Save as Draft
            </Button>
            <Button 
              className="bg-blue-600 hover:bg-blue-700"
              disabled={completedDocs < totalRequired}
            >
              Submit for Analysis
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
