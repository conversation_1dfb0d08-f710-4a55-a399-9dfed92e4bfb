"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import ThemeToggle from "@/components/ui/theme-toggle";
import { motion } from "framer-motion";
import { ArrowRight, CheckCircle, FileText, Shield, TrendingUp, Upload, Users, Zap } from "lucide-react";
import Link from "next/link";

export default function SMEPage() {
  const benefits = [
    {
      icon: TrendingUp,
      title: "Automated Financial Health Score",
      description: "Get instant evaluation of your business financial health with our AI-powered scoring system.",
      color: "text-blue-400",
      bgColor: "bg-blue-500/20",
    },
    {
      icon: Upload,
      title: "Secure Document Upload",
      description: "Upload financial statements, tax returns, and bank statements with enterprise-grade security.",
      color: "text-emerald-400",
      bgColor: "bg-emerald-500/20",
    },
    {
      icon: FileText,
      title: "Detailed Analysis Reports",
      description: "Receive comprehensive reports with actionable insights and improvement recommendations.",
      color: "text-purple-400",
      bgColor: "bg-purple-500/20",
    },
    {
      icon: Users,
      title: "Investor Matching",
      description: "Connect with pre-qualified investors who are interested in businesses like yours.",
      color: "text-amber-400",
      bgColor: "bg-amber-500/20",
    },
    {
      icon: Shield,
      title: "Data Privacy & Security",
      description: "Your sensitive business data is protected with bank-level encryption and security measures.",
      color: "text-red-400",
      bgColor: "bg-red-500/20",
    },
    {
      icon: Zap,
      title: "10X Growth Hack Access",
      description: "Exclusive access to our premium growth acceleration program with expert mentorship.",
      color: "text-yellow-400",
      bgColor: "bg-yellow-500/20",
    },
  ];

  const steps = [
    {
      step: "01",
      title: "Create Account",
      description: "Sign up and complete your business profile with basic information.",
    },
    {
      step: "02",
      title: "Upload Documents",
      description: "Securely upload your financial statements and business documents.",
    },
    {
      step: "03",
      title: "Get Analysis",
      description: "Receive your automated financial health score and detailed insights.",
    },
    {
      step: "04",
      title: "Connect with Investors",
      description: "Get matched with relevant investors and start conversations.",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-12 h-12 relative">
              <img
                src="/tenxcfo.png"
                alt="TenxCFO Logo"
                width={48}
                height={48}
                className="w-full h-full object-contain"
              />
            </div>
          </Link>
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/sme" className="text-white font-semibold border-b-2 border-blue-400">For SMEs</Link>
            <Link href="/investor" className="text-slate-300 hover:text-white transition-colors">For Investors</Link>
            <Link href="/consultant" className="text-slate-300 hover:text-white transition-colors">For Consultants</Link>
            <Link href="/10x-growth-hack" className="text-slate-300 hover:text-white transition-colors">10X Growth</Link>
            <Link href="/pricing" className="text-slate-300 hover:text-white transition-colors">Pricing</Link>
            <Link href="/auth/signin" className="text-slate-300 hover:text-white transition-colors">Log in</Link>
            <Link href="/sme/signup">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6">
                Get Started
              </Button>
            </Link>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-6 px-6 py-2 text-sm font-medium bg-blue-500/10 text-blue-300 border-blue-500/20">
              🚀 For SME Owners
            </Badge>
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-8 leading-tight">
              Get Your Business
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-emerald-400">
                Investment Ready
              </span>
            </h1>
            <p className="text-xl text-slate-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Transform your business with data-driven insights, automated financial scoring,
              and direct access to a curated network of investors.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/sme/signup">
                <Button size="lg" className="px-8 py-4 text-lg bg-blue-600 hover:bg-blue-700 text-white rounded-full">
                  Start Free Analysis <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button variant="outline" size="lg" className="px-8 py-4 text-lg border-white/30 text-white hover:bg-white/10 rounded-full">
                  View Sample Report
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-slate-800/30">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">Why Choose CFOx for Your SME?</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              Our platform is specifically designed to help small and medium enterprises access funding
              and grow their business with data-driven insights.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-blue-500/50 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className={`w-16 h-16 ${benefit.bgColor} rounded-2xl flex items-center justify-center mb-6`}>
                      <benefit.icon className={`w-8 h-8 ${benefit.color}`} />
                    </div>
                    <h3 className="text-xl font-bold text-white mb-4">{benefit.title}</h3>
                    <p className="text-slate-300">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">How It Works</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              Get started in just 4 simple steps and unlock your business potential.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto">
            {steps.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{step.title}</h3>
                <p className="text-slate-400">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-4 bg-slate-800/30">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-white mb-4">Choose Your Plan</h2>
            <p className="text-slate-300 max-w-2xl mx-auto text-lg">
              Start free and upgrade as your business grows. All plans include our core evaluation features.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Free Plan */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-blue-500/50 transition-all duration-300">
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-white mb-2">Starter</h3>
                    <div className="text-4xl font-bold text-blue-400 mb-2">Free</div>
                    <p className="text-slate-400">Perfect for getting started</p>
                  </div>
                  <ul className="space-y-3 mb-8">
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Basic financial health score
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Document upload (up to 5 files)
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Basic report generation
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Email support
                    </li>
                  </ul>
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                    Get Started Free
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Pro Plan */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-slate-800/50 border-emerald-500/50 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-emerald-500 text-white px-4 py-1">Most Popular</Badge>
                </div>
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-white mb-2">Professional</h3>
                    <div className="text-4xl font-bold text-emerald-400 mb-2">$39<span className="text-lg text-slate-400">/month</span></div>
                    <p className="text-slate-400">For growing businesses</p>
                  </div>
                  <ul className="space-y-3 mb-8">
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Advanced financial analysis
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Unlimited document uploads
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Detailed reports & insights
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Investor matching
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Priority support
                    </li>
                  </ul>
                  <Button className="w-full bg-emerald-600 hover:bg-emerald-700 text-white">
                    Start Pro Trial
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Premium Plan */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-slate-800/50 border-slate-700/50 hover:border-amber-500/50 transition-all duration-300">
                <CardContent className="p-8">
                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-white mb-2">Enterprise</h3>
                    <div className="text-4xl font-bold text-amber-400 mb-2">$129<span className="text-lg text-slate-400">/month</span></div>
                    <p className="text-slate-400">For established businesses</p>
                  </div>
                  <ul className="space-y-3 mb-8">
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Everything in Professional
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      10X Growth Hack access
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Dedicated advisor calls
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      Custom integrations
                    </li>
                    <li className="flex items-center text-slate-300">
                      <CheckCircle className="w-5 h-5 text-emerald-400 mr-3" />
                      White-label reports
                    </li>
                  </ul>
                  <Button className="w-full bg-amber-600 hover:bg-amber-700 text-white">
                    Contact Sales
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-emerald-600">
        <div className="container mx-auto text-center text-white">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🎯 Ready to Get Started?
            </Badge>
            <h2 className="text-4xl font-bold mb-4">Transform Your Business Today</h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
              Join hundreds of SMEs who have already improved their financial health and connected with investors.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/sme/signup">
                <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-4 rounded-full">
                  Get Started Free <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link href="/10x-growth-hack">
                <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10 text-lg px-8 py-4 rounded-full">
                  Learn About 10X Growth
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-800 py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">CF</span>
                </div>
                <span className="text-xl font-bold text-white">CFOx</span>
              </div>
              <p className="text-slate-400 mb-4">
                Connecting SMEs with smart investors through data-driven insights and automated financial scoring.
              </p>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Platform</h4>
              <ul className="space-y-2 text-slate-400">
                <li><Link href="/sme" className="hover:text-white transition-colors">For SMEs</Link></li>
                <li><Link href="/investor" className="hover:text-white transition-colors">For Investors</Link></li>
                <li><Link href="/consultant" className="hover:text-white transition-colors">For Consultants</Link></li>
                <li><Link href="/10x-growth-hack" className="hover:text-white transition-colors">10X Growth</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Resources</h4>
              <ul className="space-y-2 text-slate-400">
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Case Studies</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-white mb-4">Company</h4>
              <ul className="space-y-2 text-slate-400">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Terms</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-slate-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">&copy; 2024 CFOx. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Terms of Service</a>
              <a href="#" className="text-slate-400 hover:text-white text-sm transition-colors">Cookie Policy</a>
            </div>
          </div>
        </div>
      </footer>

      {/* Theme Toggle */}
      <ThemeToggle />
    </div>
  );
}
