"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight, Building, Calendar, DollarSign, Mail, MapPin, Phone, User } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function SMESignup() {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;
  
  const [formData, setFormData] = useState({
    // Personal Info
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    
    // Company Info
    companyName: "",
    industry: "",
    registrationNumber: "",
    gstNumber: "",
    address: "",
    city: "",
    state: "",
    pincode: "",
    
    // Business Details
    yearEstablished: "",
    employeeCount: "",
    annualRevenue: "",
    businessType: "",
    
    // Goals
    fundingGoal: "",
    useOfFunds: "",
    timeline: ""
  });

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = (currentStep / totalSteps) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/sme" className="flex items-center space-x-3">
            <div className="w-8 h-8 relative">
              <img
                src="/tenxcfo.png"
                alt="TenxCFO Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </Link>
          <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
            SME Registration
          </Badge>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Progress Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-white">SME Registration</h1>
            <span className="text-slate-400">Step {currentStep} of {totalSteps}</span>
          </div>
          <Progress value={progress} className="h-2 mb-2" />
          <p className="text-slate-400 text-sm">Complete your profile to get started with CFOx</p>
        </motion.div>

        {/* Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card className="bg-slate-800/50 border-slate-700/50">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                {currentStep === 1 && <><User className="w-5 h-5 mr-2 text-blue-400" />Personal Information</>}
                {currentStep === 2 && <><Building className="w-5 h-5 mr-2 text-emerald-400" />Company Details</>}
                {currentStep === 3 && <><DollarSign className="w-5 h-5 mr-2 text-amber-400" />Business Information</>}
                {currentStep === 4 && <><ArrowRight className="w-5 h-5 mr-2 text-purple-400" />Funding Goals</>}
              </CardTitle>
              <CardDescription className="text-slate-400">
                {currentStep === 1 && "Tell us about yourself"}
                {currentStep === 2 && "Provide your company information"}
                {currentStep === 3 && "Share your business details"}
                {currentStep === 4 && "Define your funding objectives"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Step 1: Personal Information */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-slate-300">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData("firstName", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-slate-300">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData("lastName", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="Doe"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-slate-300">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateFormData("email", e.target.value)}
                        className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-slate-300">Phone Number *</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => updateFormData("phone", e.target.value)}
                        className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                        placeholder="+91 98765 43210"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Company Details */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="companyName" className="text-slate-300">Company Name *</Label>
                    <div className="relative">
                      <Building className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="companyName"
                        value={formData.companyName}
                        onChange={(e) => updateFormData("companyName", e.target.value)}
                        className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                        placeholder="Your Company Ltd."
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="industry" className="text-slate-300">Industry *</Label>
                      <select
                        id="industry"
                        value={formData.industry}
                        onChange={(e) => updateFormData("industry", e.target.value)}
                        className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="">Select Industry</option>
                        <option value="technology">Technology</option>
                        <option value="manufacturing">Manufacturing</option>
                        <option value="retail">Retail</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="finance">Finance</option>
                        <option value="education">Education</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="registrationNumber" className="text-slate-300">Registration Number</Label>
                      <Input
                        id="registrationNumber"
                        value={formData.registrationNumber}
                        onChange={(e) => updateFormData("registrationNumber", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="CIN/Registration No."
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="gstNumber" className="text-slate-300">GST Number</Label>
                    <Input
                      id="gstNumber"
                      value={formData.gstNumber}
                      onChange={(e) => updateFormData("gstNumber", e.target.value)}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="GST Number (if applicable)"
                    />
                  </div>
                  <div>
                    <Label htmlFor="address" className="text-slate-300">Business Address *</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                      <Input
                        id="address"
                        value={formData.address}
                        onChange={(e) => updateFormData("address", e.target.value)}
                        className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                        placeholder="Street Address"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="city" className="text-slate-300">City *</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => updateFormData("city", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="City"
                      />
                    </div>
                    <div>
                      <Label htmlFor="state" className="text-slate-300">State *</Label>
                      <Input
                        id="state"
                        value={formData.state}
                        onChange={(e) => updateFormData("state", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="State"
                      />
                    </div>
                    <div>
                      <Label htmlFor="pincode" className="text-slate-300">Pincode *</Label>
                      <Input
                        id="pincode"
                        value={formData.pincode}
                        onChange={(e) => updateFormData("pincode", e.target.value)}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="123456"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Business Information */}
              {currentStep === 3 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="yearEstablished" className="text-slate-300">Year Established *</Label>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                        <Input
                          id="yearEstablished"
                          value={formData.yearEstablished}
                          onChange={(e) => updateFormData("yearEstablished", e.target.value)}
                          className="pl-10 bg-slate-700/50 border-slate-600 text-white"
                          placeholder="2020"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="employeeCount" className="text-slate-300">Employee Count *</Label>
                      <select
                        id="employeeCount"
                        value={formData.employeeCount}
                        onChange={(e) => updateFormData("employeeCount", e.target.value)}
                        className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                      >
                        <option value="">Select Range</option>
                        <option value="1-10">1-10</option>
                        <option value="11-50">11-50</option>
                        <option value="51-200">51-200</option>
                        <option value="201-500">201-500</option>
                        <option value="500+">500+</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="annualRevenue" className="text-slate-300">Annual Revenue *</Label>
                    <select
                      id="annualRevenue"
                      value={formData.annualRevenue}
                      onChange={(e) => updateFormData("annualRevenue", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Range</option>
                      <option value="0-130K">$0 - $130K</option>
                      <option value="130K-650K">$130K - $650K</option>
                      <option value="650K-1.3M">$650K - $1.3M</option>
                      <option value="1.3M-6.5M">$1.3M - $6.5M</option>
                      <option value="6.5M-13M">$6.5M - $13M</option>
                      <option value="13M+">$13M+</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="businessType" className="text-slate-300">Business Type *</Label>
                    <select
                      id="businessType"
                      value={formData.businessType}
                      onChange={(e) => updateFormData("businessType", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Type</option>
                      <option value="product">Product-based</option>
                      <option value="service">Service-based</option>
                      <option value="hybrid">Hybrid (Product + Service)</option>
                      <option value="trading">Trading</option>
                      <option value="manufacturing">Manufacturing</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Step 4: Funding Goals */}
              {currentStep === 4 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="fundingGoal" className="text-slate-300">Funding Goal *</Label>
                    <select
                      id="fundingGoal"
                      value={formData.fundingGoal}
                      onChange={(e) => updateFormData("fundingGoal", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Amount</option>
                      <option value="130K-650K">$130K - $650K</option>
                      <option value="650K-1.3M">$650K - $1.3M</option>
                      <option value="1.3M-6.5M">$1.3M - $6.5M</option>
                      <option value="6.5M-13M">$6.5M - $13M</option>
                      <option value="13M+">$13M+</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="useOfFunds" className="text-slate-300">Primary Use of Funds *</Label>
                    <select
                      id="useOfFunds"
                      value={formData.useOfFunds}
                      onChange={(e) => updateFormData("useOfFunds", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Purpose</option>
                      <option value="expansion">Business Expansion</option>
                      <option value="equipment">Equipment Purchase</option>
                      <option value="inventory">Inventory/Working Capital</option>
                      <option value="marketing">Marketing & Sales</option>
                      <option value="technology">Technology Upgrade</option>
                      <option value="hiring">Team Expansion</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="timeline" className="text-slate-300">Funding Timeline *</Label>
                    <select
                      id="timeline"
                      value={formData.timeline}
                      onChange={(e) => updateFormData("timeline", e.target.value)}
                      className="w-full bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select Timeline</option>
                      <option value="immediate">Immediate (Within 1 month)</option>
                      <option value="3months">Within 3 months</option>
                      <option value="6months">Within 6 months</option>
                      <option value="1year">Within 1 year</option>
                      <option value="flexible">Flexible timeline</option>
                    </select>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-between mt-8"
        >
          <div>
            {currentStep > 1 ? (
              <Button variant="outline" onClick={prevStep} className="border-slate-600 text-white hover:bg-slate-700">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
            ) : (
              <Link href="/sme">
                <Button variant="outline" className="border-slate-600 text-white hover:bg-slate-700">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to SME Page
                </Button>
              </Link>
            )}
          </div>
          
          <div>
            {currentStep < totalSteps ? (
              <Button onClick={nextStep} className="bg-blue-600 hover:bg-blue-700">
                Next Step
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Link href="/sme/dashboard">
                <Button className="bg-emerald-600 hover:bg-emerald-700">
                  Complete Registration
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
