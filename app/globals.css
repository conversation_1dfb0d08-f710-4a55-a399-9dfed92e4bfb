@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: Inter, system-ui, sans-serif;
  --font-mono: JetBrains Mono, monospace;

  /* Premium Dark Theme Colors */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-900: #0c4a6e;

  /* Sophisticated Green Accents */
  --color-secondary-50: #f0fdf4;
  --color-secondary-100: #dcfce7;
  --color-secondary-500: #22c55e;
  --color-secondary-600: #16a34a;
  --color-secondary-700: #15803d;

  /* Premium Gold Accents */
  --color-accent-50: #fffbeb;
  --color-accent-100: #fef3c7;
  --color-accent-500: #f59e0b;
  --color-accent-600: #d97706;
  --color-accent-700: #b45309;

  /* Dark Theme Colors */
  --color-dark-50: #f8fafc;
  --color-dark-100: #f1f5f9;
  --color-dark-200: #e2e8f0;
  --color-dark-300: #cbd5e1;
  --color-dark-400: #94a3b8;
  --color-dark-500: #64748b;
  --color-dark-600: #475569;
  --color-dark-700: #334155;
  --color-dark-800: #1e293b;
  --color-dark-900: #0f172a;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --primary: #0284c7;
  --primary-foreground: #ffffff;
  --secondary: #334155;
  --secondary-foreground: #f8fafc;
  --muted: #475569;
  --muted-foreground: #94a3b8;
  --accent: #334155;
  --accent-foreground: #f8fafc;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: #0284c7;
  --chart-1: #0ea5e9;
  --chart-2: #22c55e;
  --chart-3: #f59e0b;
  --chart-4: #8b5cf6;
  --chart-5: #ef4444;
  --sidebar: #0f172a;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #0284c7;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #1e293b;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #334155;
  --sidebar-ring: #0284c7;
}

.light {
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #0284c7;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f8fafc;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #0284c7;
  --chart-1: #0ea5e9;
  --chart-2: #22c55e;
  --chart-3: #f59e0b;
  --chart-4: #8b5cf6;
  --chart-5: #ef4444;
  --sidebar: #0f172a;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #0284c7;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #1e293b;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #334155;
  --sidebar-ring: #0284c7;
}

/* V2 Theme Overrides - High Specificity */
body.theme-v2 {
  background: #ffffff !important;
  color: #000000 !important;
}

/* V2 Background Overrides */
body.theme-v2 .bg-gradient-to-br {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
}

body.theme-v2 .from-slate-900,
body.theme-v2 .via-slate-800,
body.theme-v2 .to-slate-900 {
  background: #ffffff !important;
}

/* V2 Header Overrides */
body.theme-v2 header {
  background: rgba(255, 255, 255, 0.9) !important;
  border-bottom: 1px solid #e5e7eb !important;
  backdrop-filter: blur(8px) !important;
}

/* V2 Header Text and Icon Fixes */
body.theme-v2 header .text-white,
body.theme-v2 header span.text-white,
body.theme-v2 header .font-bold.text-white {
  color: #000000 !important;
}

body.theme-v2 header .text-slate-300,
body.theme-v2 header .text-slate-400 {
  color: #6b7280 !important;
}

body.theme-v2 header .hover\:text-white:hover {
  color: #000000 !important;
}

/* V2 Header Button Fixes */
body.theme-v2 header button.text-slate-300,
body.theme-v2 header button .text-slate-300,
body.theme-v2 header .text-slate-300 {
  color: #6b7280 !important;
}

body.theme-v2 header button:hover,
body.theme-v2 header button.hover\:text-white:hover,
body.theme-v2 header .hover\:text-white:hover {
  color: #000000 !important;
}

/* V2 Header Logo Background Fix */
body.theme-v2 header .bg-blue-600,
body.theme-v2 header .bg-emerald-600,
body.theme-v2 header .bg-amber-600 {
  background: #000000 !important;
}

body.theme-v2 header .bg-blue-600 span,
body.theme-v2 header .bg-emerald-600 span,
body.theme-v2 header .bg-amber-600 span {
  color: #ffffff !important;
}

/* V2 Header Notification Badge Fix */
body.theme-v2 header .bg-red-500 {
  background: #dc2626 !important;
}

/* V2 Header Ghost Button Fixes */
body.theme-v2 header button[variant="ghost"],
body.theme-v2 header .variant-ghost {
  color: #6b7280 !important;
  background: transparent !important;
}

body.theme-v2 header button[variant="ghost"]:hover,
body.theme-v2 header .variant-ghost:hover {
  color: #000000 !important;
  background: #f3f4f6 !important;
}

/* V2 Header Icon Colors */
body.theme-v2 header svg,
body.theme-v2 header .lucide {
  color: inherit !important;
}

/* V2 Header Icons - Force Visibility */
body.theme-v2 header .w-4.h-4,
body.theme-v2 header .w-5.h-5,
body.theme-v2 header .w-6.h-6,
body.theme-v2 header svg.w-4,
body.theme-v2 header svg.w-5,
body.theme-v2 header svg.w-6 {
  color: #6b7280 !important;
  stroke: currentColor !important;
  fill: none !important;
}

/* V2 Header Button Icon Containers */
body.theme-v2 header button svg,
body.theme-v2 header button .lucide,
body.theme-v2 header a svg,
body.theme-v2 header a .lucide {
  color: #6b7280 !important;
  stroke: currentColor !important;
}

/* V2 Header Button Hover Icons */
body.theme-v2 header button:hover svg,
body.theme-v2 header button:hover .lucide,
body.theme-v2 header a:hover svg,
body.theme-v2 header a:hover .lucide {
  color: #000000 !important;
  stroke: currentColor !important;
}

/* V2 Specific Header Icon Classes */
body.theme-v2 header .bell,
body.theme-v2 header .settings,
body.theme-v2 header .log-out,
body.theme-v2 header .logout,
body.theme-v2 header [data-lucide="bell"],
body.theme-v2 header [data-lucide="settings"],
body.theme-v2 header [data-lucide="log-out"],
body.theme-v2 header [data-lucide="logout"] {
  color: #6b7280 !important;
  stroke: currentColor !important;
  fill: none !important;
}

/* V2 Header Ghost Button Icons - Most Specific */
body.theme-v2 header button[variant="ghost"] svg,
body.theme-v2 header button.variant-ghost svg,
body.theme-v2 header .text-slate-300 svg {
  color: #6b7280 !important;
  stroke: currentColor !important;
  fill: none !important;
}

body.theme-v2 header button[variant="ghost"]:hover svg,
body.theme-v2 header button.variant-ghost:hover svg,
body.theme-v2 header .text-slate-300:hover svg {
  color: #000000 !important;
  stroke: currentColor !important;
  fill: none !important;
}

/* V2 Force All Header SVG Elements */
body.theme-v2 header * svg {
  color: #6b7280 !important;
  stroke: currentColor !important;
  fill: none !important;
}

body.theme-v2 header *:hover svg {
  color: #000000 !important;
  stroke: currentColor !important;
  fill: none !important;
}

/* V2 Ultra-Aggressive Header Icon Fix */
body.theme-v2 header svg,
body.theme-v2 header .lucide,
body.theme-v2 header [class*="lucide"],
body.theme-v2 header i,
body.theme-v2 header .icon {
  color: #000000 !important;
  stroke: #000000 !important;
  fill: none !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline !important;
}

/* V2 Header Button Content */
body.theme-v2 header button,
body.theme-v2 header button *,
body.theme-v2 header a,
body.theme-v2 header a * {
  color: #000000 !important;
}

/* V2 Form Element Fixes */
body.theme-v2 input,
body.theme-v2 textarea,
body.theme-v2 select,
body.theme-v2 .input,
body.theme-v2 [type="email"],
body.theme-v2 [type="password"],
body.theme-v2 [type="text"] {
  background: #ffffff !important;
  color: #000000 !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
}

body.theme-v2 input:focus,
body.theme-v2 textarea:focus,
body.theme-v2 select:focus {
  border-color: #000000 !important;
  box-shadow: 0 0 0 1px #000000 !important;
  outline: none !important;
}

/* V2 Form Labels */
body.theme-v2 label,
body.theme-v2 .label {
  color: #000000 !important;
  font-weight: 500 !important;
}

/* V2 Divider Text Fix */
body.theme-v2 .bg-slate-800,
body.theme-v2 .bg-slate-700,
body.theme-v2 .bg-slate-600,
body.theme-v2 .bg-gray-800,
body.theme-v2 .bg-gray-700 {
  background: #374151 !important;
  color: #ffffff !important;
}

/* V2 Form Placeholder Text */
body.theme-v2 input::placeholder,
body.theme-v2 textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

/* V2 Input Icons */
body.theme-v2 input + svg,
body.theme-v2 .input-container svg,
body.theme-v2 .relative svg {
  color: #6b7280 !important;
  stroke: currentColor !important;
}

/* V2 Button in Forms */
body.theme-v2 form button,
body.theme-v2 .form button {
  background: #000000 !important;
  color: #ffffff !important;
  border: 1px solid #000000 !important;
}

body.theme-v2 form button:hover,
body.theme-v2 .form button:hover {
  background: #374151 !important;
  color: #ffffff !important;
}

/* V2 Card Form Backgrounds */
body.theme-v2 .bg-slate-800\/50,
body.theme-v2 .bg-slate-900\/50,
body.theme-v2 .bg-gray-800\/50 {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

/* V2 Header Link Colors */
body.theme-v2 header a.text-slate-300 {
  color: #6b7280 !important;
}

body.theme-v2 header a.text-slate-300:hover,
body.theme-v2 header a.hover\:text-white:hover {
  color: #000000 !important;
}

body.theme-v2 .border-slate-700\/50 {
  border-color: #e5e7eb !important;
}

body.theme-v2 .bg-slate-900\/80 {
  background: rgba(255, 255, 255, 0.9) !important;
}

/* V2 Text Overrides */
body.theme-v2 .text-white {
  color: #000000 !important;
}

body.theme-v2 .text-slate-300 {
  color: #6b7280 !important;
}

body.theme-v2 .text-slate-400 {
  color: #9ca3af !important;
}

body.theme-v2 .text-slate-500 {
  color: #6b7280 !important;
}

/* V2 Button Overrides */
body.theme-v2 .bg-white {
  background: #000000 !important;
  color: #ffffff !important;
}

body.theme-v2 .hover\:bg-slate-100:hover {
  background: #374151 !important;
}

body.theme-v2 .text-slate-900 {
  color: #ffffff !important;
}

/* V2 Card Overrides */
body.theme-v2 .bg-gradient-to-br.from-slate-800\/50 {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

body.theme-v2 .bg-slate-900\/80 {
  background: #f9fafb !important;
  border-color: #e5e7eb !important;
}

body.theme-v2 .border-slate-700\/30,
body.theme-v2 .border-slate-700\/50 {
  border-color: #e5e7eb !important;
}

/* V2 Deal Pipeline Card Backgrounds */
body.theme-v2 .bg-slate-700\/30 {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

body.theme-v2 .bg-slate-800\/50 {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* V2 Badge Overrides - Pure B&W */
body.theme-v2 .bg-blue-500\/10 {
  background: #f3f4f6 !important;
}

body.theme-v2 .text-blue-300 {
  color: #000000 !important;
}

body.theme-v2 .border-blue-500\/20 {
  border-color: #d1d5db !important;
}

body.theme-v2 .text-blue-400 {
  color: #000000 !important;
}

body.theme-v2 .text-blue-600 {
  color: #000000 !important;
}

/* V2 Gradient Text Override - Pure B&W */
body.theme-v2 .bg-clip-text.text-transparent {
  color: #000000 !important;
  background: none !important;
  -webkit-background-clip: unset !important;
  background-clip: unset !important;
}

/* V2 Button Variants - Pure B&W */
body.theme-v2 .bg-blue-600 {
  background: #000000 !important;
  color: #ffffff !important;
}

body.theme-v2 .hover\:bg-blue-700:hover {
  background: #374151 !important;
  color: #ffffff !important;
}

/* V2 All Blue Colors to Black with White Text */
body.theme-v2 .bg-blue-600 {
  background: #000000 !important;
  color: #ffffff !important;
}

body.theme-v2 .text-emerald-400 {
  color: #000000 !important;
}

body.theme-v2 .bg-emerald-500\/20 {
  background: #f3f4f6 !important;
}

body.theme-v2 .text-amber-400 {
  color: #000000 !important;
}

body.theme-v2 .bg-amber-600 {
  background: #000000 !important;
}

body.theme-v2 .bg-emerald-600 {
  background: #000000 !important;
}

/* V2 Logo and Brand Colors - Pure B&W */
body.theme-v2 .w-8.h-8.bg-blue-600 {
  background: #000000 !important;
}

body.theme-v2 .border-white\/30 {
  border-color: #d1d5db !important;
}

body.theme-v2 .hover\:bg-white\/10:hover {
  background: #f3f4f6 !important;
  color: #000000 !important;
}

/* V2 Comprehensive Color Overrides - Pure Black & White */

/* All Slate Colors to Grayscale */
body.theme-v2 .text-slate-100 { color: #000000 !important; }
body.theme-v2 .text-slate-200 { color: #000000 !important; }
body.theme-v2 .text-slate-300 { color: #6b7280 !important; }
body.theme-v2 .text-slate-400 { color: #9ca3af !important; }
body.theme-v2 .text-slate-500 { color: #6b7280 !important; }
body.theme-v2 .text-slate-600 { color: #374151 !important; }
body.theme-v2 .text-slate-700 { color: #000000 !important; }
body.theme-v2 .text-slate-800 { color: #000000 !important; }
body.theme-v2 .text-slate-900 { color: #000000 !important; }

/* All Background Slate Colors */
body.theme-v2 .bg-slate-50 { background: #ffffff !important; }
body.theme-v2 .bg-slate-100 { background: #f9fafb !important; }
body.theme-v2 .bg-slate-200 { background: #f3f4f6 !important; }
body.theme-v2 .bg-slate-300 { background: #e5e7eb !important; }
body.theme-v2 .bg-slate-400 { background: #d1d5db !important; }
body.theme-v2 .bg-slate-500 { background: #9ca3af !important; }
body.theme-v2 .bg-slate-600 { background: #6b7280 !important; }
body.theme-v2 .bg-slate-700 { background: #374151 !important; }
body.theme-v2 .bg-slate-800 { background: #1f2937 !important; }
body.theme-v2 .bg-slate-900 { background: #000000 !important; }

/* All Border Slate Colors */
body.theme-v2 .border-slate-100 { border-color: #f3f4f6 !important; }
body.theme-v2 .border-slate-200 { border-color: #e5e7eb !important; }
body.theme-v2 .border-slate-300 { border-color: #d1d5db !important; }
body.theme-v2 .border-slate-400 { border-color: #9ca3af !important; }
body.theme-v2 .border-slate-500 { border-color: #6b7280 !important; }
body.theme-v2 .border-slate-600 { border-color: #374151 !important; }
body.theme-v2 .border-slate-700 { border-color: #1f2937 !important; }
body.theme-v2 .border-slate-800 { border-color: #000000 !important; }
body.theme-v2 .border-slate-900 { border-color: #000000 !important; }

/* All Blue Colors to Black/White */
body.theme-v2 .text-blue-50 { color: #000000 !important; }
body.theme-v2 .text-blue-100 { color: #000000 !important; }
body.theme-v2 .text-blue-200 { color: #000000 !important; }
body.theme-v2 .text-blue-300 { color: #000000 !important; }
body.theme-v2 .text-blue-400 { color: #000000 !important; }
body.theme-v2 .text-blue-500 { color: #000000 !important; }
body.theme-v2 .text-blue-600 { color: #000000 !important; }
body.theme-v2 .text-blue-700 { color: #000000 !important; }
body.theme-v2 .text-blue-800 { color: #000000 !important; }
body.theme-v2 .text-blue-900 { color: #000000 !important; }

body.theme-v2 .bg-blue-50 { background: #f9fafb !important; }
body.theme-v2 .bg-blue-100 { background: #f3f4f6 !important; }
body.theme-v2 .bg-blue-200 { background: #e5e7eb !important; }
body.theme-v2 .bg-blue-300 { background: #d1d5db !important; }
body.theme-v2 .bg-blue-400 { background: #9ca3af !important; }
body.theme-v2 .bg-blue-500 { background: #6b7280 !important; }
body.theme-v2 .bg-blue-600 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-blue-700 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-blue-800 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-blue-900 { background: #000000 !important; color: #ffffff !important; }

body.theme-v2 .border-blue-100 { border-color: #f3f4f6 !important; }
body.theme-v2 .border-blue-200 { border-color: #e5e7eb !important; }
body.theme-v2 .border-blue-300 { border-color: #d1d5db !important; }
body.theme-v2 .border-blue-400 { border-color: #9ca3af !important; }
body.theme-v2 .border-blue-500 { border-color: #6b7280 !important; }
body.theme-v2 .border-blue-600 { border-color: #000000 !important; }
body.theme-v2 .border-blue-700 { border-color: #000000 !important; }
body.theme-v2 .border-blue-800 { border-color: #000000 !important; }
body.theme-v2 .border-blue-900 { border-color: #000000 !important; }

/* All Emerald/Green Colors to Black/White */
body.theme-v2 .text-emerald-50 { color: #000000 !important; }
body.theme-v2 .text-emerald-100 { color: #000000 !important; }
body.theme-v2 .text-emerald-200 { color: #000000 !important; }
body.theme-v2 .text-emerald-300 { color: #000000 !important; }
body.theme-v2 .text-emerald-400 { color: #000000 !important; }
body.theme-v2 .text-emerald-500 { color: #000000 !important; }
body.theme-v2 .text-emerald-600 { color: #000000 !important; }
body.theme-v2 .text-emerald-700 { color: #000000 !important; }
body.theme-v2 .text-emerald-800 { color: #000000 !important; }
body.theme-v2 .text-emerald-900 { color: #000000 !important; }

body.theme-v2 .bg-emerald-50 { background: #f9fafb !important; }
body.theme-v2 .bg-emerald-100 { background: #f3f4f6 !important; }
body.theme-v2 .bg-emerald-200 { background: #e5e7eb !important; }
body.theme-v2 .bg-emerald-300 { background: #d1d5db !important; }
body.theme-v2 .bg-emerald-400 { background: #9ca3af !important; }
body.theme-v2 .bg-emerald-500 { background: #6b7280 !important; }
body.theme-v2 .bg-emerald-600 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-emerald-700 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-emerald-800 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-emerald-900 { background: #000000 !important; color: #ffffff !important; }

body.theme-v2 .border-emerald-100 { border-color: #f3f4f6 !important; }
body.theme-v2 .border-emerald-200 { border-color: #e5e7eb !important; }
body.theme-v2 .border-emerald-300 { border-color: #d1d5db !important; }
body.theme-v2 .border-emerald-400 { border-color: #9ca3af !important; }
body.theme-v2 .border-emerald-500 { border-color: #6b7280 !important; }
body.theme-v2 .border-emerald-600 { border-color: #000000 !important; }
body.theme-v2 .border-emerald-700 { border-color: #000000 !important; }
body.theme-v2 .border-emerald-800 { border-color: #000000 !important; }
body.theme-v2 .border-emerald-900 { border-color: #000000 !important; }

/* All Amber/Yellow Colors to Black/White */
body.theme-v2 .text-amber-50 { color: #000000 !important; }
body.theme-v2 .text-amber-100 { color: #000000 !important; }
body.theme-v2 .text-amber-200 { color: #000000 !important; }
body.theme-v2 .text-amber-300 { color: #000000 !important; }
body.theme-v2 .text-amber-400 { color: #000000 !important; }
body.theme-v2 .text-amber-500 { color: #000000 !important; }
body.theme-v2 .text-amber-600 { color: #000000 !important; }
body.theme-v2 .text-amber-700 { color: #000000 !important; }
body.theme-v2 .text-amber-800 { color: #000000 !important; }
body.theme-v2 .text-amber-900 { color: #000000 !important; }

body.theme-v2 .bg-amber-50 { background: #f9fafb !important; }
body.theme-v2 .bg-amber-100 { background: #f3f4f6 !important; }
body.theme-v2 .bg-amber-200 { background: #e5e7eb !important; }
body.theme-v2 .bg-amber-300 { background: #d1d5db !important; }
body.theme-v2 .bg-amber-400 { background: #9ca3af !important; }
body.theme-v2 .bg-amber-500 { background: #6b7280 !important; }
body.theme-v2 .bg-amber-600 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-amber-700 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-amber-800 { background: #000000 !important; color: #ffffff !important; }
body.theme-v2 .bg-amber-900 { background: #000000 !important; color: #ffffff !important; }

body.theme-v2 .border-amber-100 { border-color: #f3f4f6 !important; }
body.theme-v2 .border-amber-200 { border-color: #e5e7eb !important; }
body.theme-v2 .border-amber-300 { border-color: #d1d5db !important; }
body.theme-v2 .border-amber-400 { border-color: #9ca3af !important; }
body.theme-v2 .border-amber-500 { border-color: #6b7280 !important; }
body.theme-v2 .border-amber-600 { border-color: #000000 !important; }
body.theme-v2 .border-amber-700 { border-color: #000000 !important; }
body.theme-v2 .border-amber-800 { border-color: #000000 !important; }
body.theme-v2 .border-amber-900 { border-color: #000000 !important; }

/* Hover States for V2 */
body.theme-v2 .hover\:text-white:hover { color: #000000 !important; }
body.theme-v2 .hover\:text-slate-300:hover { color: #6b7280 !important; }
body.theme-v2 .hover\:bg-slate-100:hover { background: #f3f4f6 !important; }
body.theme-v2 .hover\:bg-slate-200:hover { background: #e5e7eb !important; }
body.theme-v2 .hover\:bg-blue-700:hover { background: #374151 !important; color: #ffffff !important; }
body.theme-v2 .hover\:bg-emerald-700:hover { background: #374151 !important; color: #ffffff !important; }
body.theme-v2 .hover\:bg-amber-700:hover { background: #374151 !important; color: #ffffff !important; }

/* Focus States for V2 */
body.theme-v2 .focus\:ring-blue-500:focus { --tw-ring-color: #000000 !important; }
body.theme-v2 .focus\:ring-emerald-500:focus { --tw-ring-color: #000000 !important; }
body.theme-v2 .focus\:ring-amber-500:focus { --tw-ring-color: #000000 !important; }

/* Active States for V2 */
body.theme-v2 .active\:bg-blue-700:active { background: #374151 !important; }
body.theme-v2 .active\:bg-emerald-700:active { background: #374151 !important; }
body.theme-v2 .active\:bg-amber-700:active { background: #374151 !important; }

/* Opacity Variants for V2 */
body.theme-v2 .bg-blue-600\/20 { background: rgba(243, 244, 246, 0.2) !important; }
body.theme-v2 .bg-emerald-500\/20 { background: rgba(243, 244, 246, 0.2) !important; }
body.theme-v2 .bg-amber-500\/20 { background: rgba(243, 244, 246, 0.2) !important; }
body.theme-v2 .bg-slate-800\/50 { background: rgba(255, 255, 255, 0.5) !important; }
body.theme-v2 .bg-slate-900\/50 { background: rgba(255, 255, 255, 0.5) !important; }

/* V2 Button Text Fixes - Ensure White Text on Black Buttons */
body.theme-v2 button.bg-blue-600,
body.theme-v2 .bg-blue-600 button,
body.theme-v2 button.bg-emerald-600,
body.theme-v2 .bg-emerald-600 button,
body.theme-v2 button.bg-amber-600,
body.theme-v2 .bg-amber-600 button,
body.theme-v2 button.bg-black,
body.theme-v2 .bg-black button {
  background: #000000 !important;
  color: #ffffff !important;
}

/* V2 Button Hover Text Fixes */
body.theme-v2 button.bg-blue-600:hover,
body.theme-v2 .bg-blue-600 button:hover,
body.theme-v2 button.bg-emerald-600:hover,
body.theme-v2 .bg-emerald-600 button:hover,
body.theme-v2 button.bg-amber-600:hover,
body.theme-v2 .bg-amber-600 button:hover,
body.theme-v2 button.bg-black:hover,
body.theme-v2 .bg-black button:hover {
  background: #374151 !important;
  color: #ffffff !important;
}

/* V2 Specific Button Classes */
body.theme-v2 .hover\:bg-blue-700:hover,
body.theme-v2 .hover\:bg-emerald-700:hover,
body.theme-v2 .hover\:bg-amber-700:hover {
  background: #374151 !important;
  color: #ffffff !important;
}

/* V2 All Button Text Overrides */
body.theme-v2 button[class*="bg-blue"],
body.theme-v2 button[class*="bg-emerald"],
body.theme-v2 button[class*="bg-amber"],
body.theme-v2 button[class*="bg-black"],
body.theme-v2 .bg-blue-600 *,
body.theme-v2 .bg-emerald-600 *,
body.theme-v2 .bg-amber-600 *,
body.theme-v2 .bg-black * {
  color: #ffffff !important;
}

/* V2 Button Component Overrides */
body.theme-v2 [role="button"][class*="bg-blue"],
body.theme-v2 [role="button"][class*="bg-emerald"],
body.theme-v2 [role="button"][class*="bg-amber"],
body.theme-v2 [role="button"][class*="bg-black"] {
  background: #000000 !important;
  color: #ffffff !important;
}

/* V2 Link Button Overrides */
body.theme-v2 a[class*="bg-blue"],
body.theme-v2 a[class*="bg-emerald"],
body.theme-v2 a[class*="bg-amber"],
body.theme-v2 a[class*="bg-black"] {
  background: #000000 !important;
  color: #ffffff !important;
}

/* V2 Force White Text on All Dark Buttons */
body.theme-v2 button,
body.theme-v2 [role="button"],
body.theme-v2 .bg-blue-600,
body.theme-v2 .bg-blue-700,
body.theme-v2 .bg-blue-800,
body.theme-v2 .bg-blue-900,
body.theme-v2 .bg-emerald-600,
body.theme-v2 .bg-emerald-700,
body.theme-v2 .bg-emerald-800,
body.theme-v2 .bg-emerald-900,
body.theme-v2 .bg-amber-600,
body.theme-v2 .bg-amber-700,
body.theme-v2 .bg-amber-800,
body.theme-v2 .bg-amber-900,
body.theme-v2 .bg-black {
  color: #ffffff !important;
}

/* V2 Force White Text on Button Children */
body.theme-v2 button *,
body.theme-v2 [role="button"] *,
body.theme-v2 .bg-blue-600 *,
body.theme-v2 .bg-emerald-600 *,
body.theme-v2 .bg-amber-600 *,
body.theme-v2 .bg-black * {
  color: #ffffff !important;
}

/* V2 Override Any Remaining Text Colors in Buttons */
body.theme-v2 button span,
body.theme-v2 button div,
body.theme-v2 button p,
body.theme-v2 [role="button"] span,
body.theme-v2 [role="button"] div,
body.theme-v2 [role="button"] p,
body.theme-v2 .bg-blue-600 span,
body.theme-v2 .bg-blue-600 div,
body.theme-v2 .bg-emerald-600 span,
body.theme-v2 .bg-emerald-600 div,
body.theme-v2 .bg-amber-600 span,
body.theme-v2 .bg-amber-600 div,
body.theme-v2 .bg-black span,
body.theme-v2 .bg-black div {
  color: #ffffff !important;
}

/* V2 Override Text Color Classes in Buttons */
body.theme-v2 button .text-slate-900,
body.theme-v2 button .text-black,
body.theme-v2 button .text-gray-900,
body.theme-v2 [role="button"] .text-slate-900,
body.theme-v2 [role="button"] .text-black,
body.theme-v2 [role="button"] .text-gray-900 {
  color: #ffffff !important;
}

/* V2 Specific Button Text Overrides - Target All Button Scenarios */
body.theme-v2 button.bg-white,
body.theme-v2 .bg-white button,
body.theme-v2 a.bg-white,
body.theme-v2 .bg-white a {
  background: #000000 !important;
  color: #ffffff !important;
}

/* V2 Override White Background Buttons */
body.theme-v2 .bg-white.text-slate-900,
body.theme-v2 .bg-white .text-slate-900,
body.theme-v2 button.bg-white.text-slate-900,
body.theme-v2 .bg-white button.text-slate-900 {
  background: #000000 !important;
  color: #ffffff !important;
}

/* V2 Force All Button Text to White - Most Specific */
body.theme-v2 button[class*="bg-"],
body.theme-v2 [role="button"][class*="bg-"],
body.theme-v2 a[class*="bg-"] {
  color: #ffffff !important;
}

/* V2 Override Any Text Color Classes in Buttons */
body.theme-v2 button .text-slate-900,
body.theme-v2 button .text-black,
body.theme-v2 button .text-gray-900,
body.theme-v2 button .text-white,
body.theme-v2 [role="button"] .text-slate-900,
body.theme-v2 [role="button"] .text-black,
body.theme-v2 [role="button"] .text-gray-900,
body.theme-v2 [role="button"] .text-white,
body.theme-v2 a .text-slate-900,
body.theme-v2 a .text-black,
body.theme-v2 a .text-gray-900,
body.theme-v2 a .text-white {
  color: #ffffff !important;
}

/* V2 Ultra-Specific Button Text Fixes - Only Target Actual Buttons */
body.theme-v2 button,
body.theme-v2 button *,
body.theme-v2 [role="button"],
body.theme-v2 [role="button"] *,
body.theme-v2 button.bg-white,
body.theme-v2 button.bg-white *,
body.theme-v2 button.bg-blue-600,
body.theme-v2 button.bg-blue-600 *,
body.theme-v2 button.bg-emerald-600,
body.theme-v2 button.bg-emerald-600 *,
body.theme-v2 button.bg-amber-600,
body.theme-v2 button.bg-amber-600 *,
body.theme-v2 button.bg-black,
body.theme-v2 button.bg-black *,
body.theme-v2 a.bg-blue-600,
body.theme-v2 a.bg-blue-600 *,
body.theme-v2 a.bg-emerald-600,
body.theme-v2 a.bg-emerald-600 *,
body.theme-v2 a.bg-amber-600,
body.theme-v2 a.bg-amber-600 * {
  color: #ffffff !important;
}

/* V2 Navigation Button Specific */
body.theme-v2 nav button,
body.theme-v2 nav button *,
body.theme-v2 nav a[class*="bg-"],
body.theme-v2 nav a[class*="bg-"] *,
body.theme-v2 header button,
body.theme-v2 header button *,
body.theme-v2 header a[class*="bg-"],
body.theme-v2 header a[class*="bg-"] * {
  color: #ffffff !important;
}

/* V2 Override Text Colors Only in Buttons */
body.theme-v2 button .text-slate-900,
body.theme-v2 button .text-gray-900,
body.theme-v2 button .text-black,
body.theme-v2 [role="button"] .text-slate-900,
body.theme-v2 [role="button"] .text-gray-900,
body.theme-v2 [role="button"] .text-black,
body.theme-v2 a.bg-white .text-slate-900,
body.theme-v2 a.bg-blue-600 .text-white,
body.theme-v2 a.bg-emerald-600 .text-white,
body.theme-v2 a.bg-amber-600 .text-white {
  color: #ffffff !important;
}

/* V2 Fix for MainNavigation Default Button - bg-white text-slate-900 */
body.theme-v2 .bg-white.text-slate-900,
body.theme-v2 button.bg-white.text-slate-900,
body.theme-v2 a.bg-white.text-slate-900,
body.theme-v2 [class*="bg-white"][class*="text-slate-900"],
body.theme-v2 button[class*="bg-white"][class*="text-slate-900"],
body.theme-v2 a[class*="bg-white"][class*="text-slate-900"] {
  background: #000000 !important;
  color: #ffffff !important;
}

/* V2 Fix for MainNavigation Hover States */
body.theme-v2 .bg-white.text-slate-900.hover\:bg-slate-100:hover,
body.theme-v2 button.bg-white.text-slate-900.hover\:bg-slate-100:hover,
body.theme-v2 a.bg-white.text-slate-900.hover\:bg-slate-100:hover {
  background: #374151 !important;
  color: #ffffff !important;
}

/* V2 Force Override Any White Background in V2 Theme - Only for Buttons */
body.theme-v2 button.bg-white,
body.theme-v2 a.bg-white,
body.theme-v2 [role="button"].bg-white {
  background: #000000 !important;
  color: #ffffff !important;
}

body.theme-v2 button.hover\:bg-slate-100:hover,
body.theme-v2 a.hover\:bg-slate-100:hover,
body.theme-v2 [role="button"].hover\:bg-slate-100:hover {
  background: #374151 !important;
  color: #ffffff !important;
}

/* Additional Color Overrides for V2 */
body.theme-v2 .text-purple-400 { color: #000000 !important; }
body.theme-v2 .bg-purple-500\/20 { background: #f3f4f6 !important; }
body.theme-v2 .text-red-400 { color: #000000 !important; }
body.theme-v2 .bg-red-500\/20 { background: #f3f4f6 !important; }
body.theme-v2 .text-yellow-400 { color: #000000 !important; }
body.theme-v2 .bg-yellow-500\/20 { background: #f3f4f6 !important; }

/* Gradient Overrides for V2 */
body.theme-v2 .bg-gradient-to-r { background: #ffffff !important; }
body.theme-v2 .from-blue-600 { background: #ffffff !important; }
body.theme-v2 .to-emerald-600 { background: #ffffff !important; }

/* Card Hover States for V2 */
body.theme-v2 .hover\:border-blue-500\/50:hover { border-color: #d1d5db !important; }
body.theme-v2 .hover\:border-amber-500\/50:hover { border-color: #d1d5db !important; }
body.theme-v2 .border-emerald-500\/50 { border-color: #d1d5db !important; }

/* Background Sections for V2 */
body.theme-v2 .bg-slate-800\/30 {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}
body.theme-v2 .bg-slate-800\/50 {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* Footer for V2 */
body.theme-v2 footer.bg-slate-900 { background: #f3f4f6 !important; }
body.theme-v2 .border-slate-800 { border-color: #e5e7eb !important; }

/* V2 Outline Button Fixes - Ensure Proper Contrast */
body.theme-v2 button[variant="outline"],
body.theme-v2 .variant-outline,
body.theme-v2 button.border,
body.theme-v2 a.border {
  background: transparent !important;
  color: #000000 !important;
  border-color: #000000 !important;
}

body.theme-v2 button[variant="outline"]:hover,
body.theme-v2 .variant-outline:hover,
body.theme-v2 button.border:hover,
body.theme-v2 a.border:hover {
  background: #000000 !important;
  color: #ffffff !important;
  border-color: #000000 !important;
}

/* V2 Ensure Cards and Other Elements Don't Get Button Styling */
body.theme-v2 .bg-white:not(button):not([role="button"]):not(a) {
  background: #ffffff !important;
  color: #000000 !important;
}

/* V2 Icon Background Color Overrides - Remove All Colors */
body.theme-v2 .bg-blue-500\/20,
body.theme-v2 .bg-blue-600\/20,
body.theme-v2 .bg-emerald-500\/20,
body.theme-v2 .bg-emerald-600\/20,
body.theme-v2 .bg-amber-500\/20,
body.theme-v2 .bg-amber-600\/20,
body.theme-v2 .bg-purple-500\/20,
body.theme-v2 .bg-purple-600\/20,
body.theme-v2 .bg-red-500\/20,
body.theme-v2 .bg-red-600\/20,
body.theme-v2 .bg-yellow-500\/20,
body.theme-v2 .bg-yellow-600\/20,
body.theme-v2 .bg-green-500\/20,
body.theme-v2 .bg-green-600\/20,
body.theme-v2 .bg-indigo-500\/20,
body.theme-v2 .bg-indigo-600\/20,
body.theme-v2 .bg-pink-500\/20,
body.theme-v2 .bg-pink-600\/20,
body.theme-v2 .bg-teal-500\/20,
body.theme-v2 .bg-teal-600\/20 {
  background: #f3f4f6 !important;
}

/* V2 Icon Container Backgrounds - All Solid Colors to Gray */
body.theme-v2 .bg-blue-50,
body.theme-v2 .bg-blue-100,
body.theme-v2 .bg-emerald-50,
body.theme-v2 .bg-emerald-100,
body.theme-v2 .bg-amber-50,
body.theme-v2 .bg-amber-100,
body.theme-v2 .bg-purple-50,
body.theme-v2 .bg-purple-100,
body.theme-v2 .bg-red-50,
body.theme-v2 .bg-red-100,
body.theme-v2 .bg-yellow-50,
body.theme-v2 .bg-yellow-100,
body.theme-v2 .bg-green-50,
body.theme-v2 .bg-green-100,
body.theme-v2 .bg-indigo-50,
body.theme-v2 .bg-indigo-100,
body.theme-v2 .bg-pink-50,
body.theme-v2 .bg-pink-100,
body.theme-v2 .bg-teal-50,
body.theme-v2 .bg-teal-100 {
  background: #f9fafb !important;
}

/* V2 Icon Text Colors - All Colors to Black */
body.theme-v2 .text-blue-400,
body.theme-v2 .text-blue-500,
body.theme-v2 .text-blue-600,
body.theme-v2 .text-emerald-400,
body.theme-v2 .text-emerald-500,
body.theme-v2 .text-emerald-600,
body.theme-v2 .text-amber-400,
body.theme-v2 .text-amber-500,
body.theme-v2 .text-amber-600,
body.theme-v2 .text-purple-400,
body.theme-v2 .text-purple-500,
body.theme-v2 .text-purple-600,
body.theme-v2 .text-red-400,
body.theme-v2 .text-red-500,
body.theme-v2 .text-red-600,
body.theme-v2 .text-yellow-400,
body.theme-v2 .text-yellow-500,
body.theme-v2 .text-yellow-600,
body.theme-v2 .text-green-400,
body.theme-v2 .text-green-500,
body.theme-v2 .text-green-600,
body.theme-v2 .text-indigo-400,
body.theme-v2 .text-indigo-500,
body.theme-v2 .text-indigo-600,
body.theme-v2 .text-pink-400,
body.theme-v2 .text-pink-500,
body.theme-v2 .text-pink-600,
body.theme-v2 .text-teal-400,
body.theme-v2 .text-teal-500,
body.theme-v2 .text-teal-600 {
  color: #000000 !important;
}

/* V2 Icon SVG Elements - Force Black Color */
body.theme-v2 svg.text-blue-400,
body.theme-v2 svg.text-emerald-400,
body.theme-v2 svg.text-amber-400,
body.theme-v2 svg.text-purple-400,
body.theme-v2 svg.text-red-400,
body.theme-v2 svg.text-yellow-400,
body.theme-v2 .text-blue-400 svg,
body.theme-v2 .text-emerald-400 svg,
body.theme-v2 .text-amber-400 svg,
body.theme-v2 .text-purple-400 svg,
body.theme-v2 .text-red-400 svg,
body.theme-v2 .text-yellow-400 svg {
  color: #000000 !important;
  fill: currentColor !important;
}

/* V2 Progress Bar Overrides - Remove Blue Colors */
body.theme-v2 .bg-blue-600,
body.theme-v2 .bg-blue-500,
body.theme-v2 .bg-blue-400,
body.theme-v2 [role="progressbar"],
body.theme-v2 .progress-bar,
body.theme-v2 .w-full.bg-blue-600,
body.theme-v2 .h-2.bg-blue-600 {
  background: #000000 !important;
}

/* V2 Progress Container */
body.theme-v2 .bg-slate-200,
body.theme-v2 .bg-gray-200,
body.theme-v2 .progress-container {
  background: #e5e7eb !important;
}

/* V2 Purple Button Override */
body.theme-v2 .bg-purple-600,
body.theme-v2 .bg-purple-500,
body.theme-v2 button.bg-purple-600,
body.theme-v2 .bg-purple-600.text-white {
  background: #000000 !important;
  color: #ffffff !important;
}

/* V2 Green Background Override */
body.theme-v2 .bg-green-50,
body.theme-v2 .bg-green-100,
body.theme-v2 .bg-emerald-50,
body.theme-v2 .bg-emerald-100 {
  background: #f9fafb !important;
}

/* V2 Action Item Backgrounds */
body.theme-v2 .bg-red-50,
body.theme-v2 .bg-orange-50,
body.theme-v2 .bg-yellow-50 {
  background: #f9fafb !important;
}

/* V2 Menu Icon Fixes - Ensure Visibility */
body.theme-v2 .lucide,
body.theme-v2 svg,
body.theme-v2 .icon {
  color: #000000 !important;
  stroke: currentColor !important;
  fill: none !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline !important;
}

/* V2 Dashboard Card Icons */
body.theme-v2 .w-4.h-4,
body.theme-v2 .w-5.h-5,
body.theme-v2 .w-6.h-6 {
  color: #000000 !important;
  stroke: #000000 !important;
  fill: none !important;
  opacity: 1 !important;
}

/* V2 Button Icons */
body.theme-v2 button svg,
body.theme-v2 button .lucide,
body.theme-v2 [role="button"] svg,
body.theme-v2 [role="button"] .lucide {
  color: #000000 !important;
  stroke: #000000 !important;
  fill: none !important;
  opacity: 1 !important;
}

/* V2 Header Button Icons Specific */
body.theme-v2 header button svg,
body.theme-v2 header button .lucide {
  color: #6b7280 !important;
  stroke: #6b7280 !important;
  fill: none !important;
  opacity: 1 !important;
}

body.theme-v2 header button:hover svg,
body.theme-v2 header button:hover .lucide {
  color: #000000 !important;
  stroke: #000000 !important;
  fill: none !important;
  opacity: 1 !important;
}

/* V2 Card Content Icons - Specific for dashboard cards */
body.theme-v2 .bg-slate-700\/30 svg,
body.theme-v2 .bg-slate-800\/50 svg,
body.theme-v2 .bg-slate-700\/30 .lucide,
body.theme-v2 .bg-slate-800\/50 .lucide {
  color: #000000 !important;
  stroke: #000000 !important;
  fill: none !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* V2 Card Content Text */
body.theme-v2 .text-slate-400,
body.theme-v2 .text-slate-500 {
  color: #6b7280 !important;
}

/* V2 Card Title and Content Text */
body.theme-v2 .text-white {
  color: #000000 !important;
}

body.theme-v2 .text-slate-300 {
  color: #374151 !important;
}

/* V2 Badge Text Colors */
body.theme-v2 .text-emerald-400,
body.theme-v2 .text-blue-400,
body.theme-v2 .text-red-400,
body.theme-v2 .text-amber-400 {
  color: #000000 !important;
}

/* V2 Deal Pipeline Card Hover States */
body.theme-v2 .hover\:border-emerald-500\/50:hover {
  border-color: #d1d5db !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* V2 Button Overrides for Cards */
body.theme-v2 .border-slate-600 {
  border-color: #d1d5db !important;
  background: #ffffff !important;
  color: #000000 !important;
}

body.theme-v2 .hover\:bg-slate-700:hover {
  background: #f3f4f6 !important;
  color: #000000 !important;
}

body.theme-v2 .bg-emerald-600 {
  background: #000000 !important;
  color: #ffffff !important;
  border: 1px solid #000000 !important;
}

body.theme-v2 .hover\:bg-emerald-700:hover {
  background: #374151 !important;
  color: #ffffff !important;
}

/* V2 Specific Icon Fixes - Force Visibility */
body.theme-v2 .lucide-eye,
body.theme-v2 .lucide-heart,
body.theme-v2 .lucide-message-circle,
body.theme-v2 .lucide-arrow-right,
body.theme-v2 .lucide-map-pin,
body.theme-v2 .lucide-dollar-sign,
body.theme-v2 .lucide-building,
body.theme-v2 .lucide-trending-up,
body.theme-v2 .lucide-search,
body.theme-v2 .lucide-filter {
  color: #000000 !important;
  stroke: #000000 !important;
  fill: none !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
  width: auto !important;
  height: auto !important;
}

/* V2 Icon Container Fixes */
body.theme-v2 .w-12.h-12.bg-emerald-500\/20,
body.theme-v2 .w-12.h-12.bg-blue-500\/20,
body.theme-v2 .w-12.h-12.bg-amber-500\/20 {
  background: #f3f4f6 !important;
  border: 1px solid #e5e7eb !important;
}

/* V2 Text Color Fixes - Comprehensive */
body.theme-v2 h1,
body.theme-v2 h2,
body.theme-v2 h3,
body.theme-v2 h4,
body.theme-v2 h5,
body.theme-v2 h6 {
  color: #000000 !important;
}

/* V2 Logo and Brand Text */
body.theme-v2 .text-2xl.font-bold,
body.theme-v2 .text-xl.font-bold,
body.theme-v2 .font-bold {
  color: #000000 !important;
}

/* V2 Theme Toggle Text */
body.theme-v2 .text-slate-300 {
  color: #6b7280 !important;
}

/* V2 Welcome Text and Descriptions */
body.theme-v2 .text-slate-400,
body.theme-v2 .text-slate-500 {
  color: #6b7280 !important;
}

/* V2 Card and Content Text */
body.theme-v2 .text-white {
  color: #000000 !important;
}

body.theme-v2 .text-slate-300 {
  color: #374151 !important;
}

/* V2 Background Overrides for Pages */
body.theme-v2 .bg-slate-900 {
  background: #ffffff !important;
}

body.theme-v2 .min-h-screen.bg-slate-900 {
  background: #f9fafb !important;
}

/* V2 Container and Layout Fixes */
body.theme-v2 .container {
  background: transparent !important;
}

/* V2 Deal Detail Page Specific Fixes */
body.theme-v2 .bg-slate-800\/50.border-slate-700\/50 {
  background: #ffffff !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* V2 Badge Status Colors */
body.theme-v2 .bg-emerald-500\/20.text-emerald-400.border-emerald-500\/30 {
  background: #dcfce7 !important;
  color: #166534 !important;
  border-color: #bbf7d0 !important;
}

body.theme-v2 .bg-blue-500\/20.text-blue-400.border-blue-500\/30 {
  background: #dbeafe !important;
  color: #1e40af !important;
  border-color: #bfdbfe !important;
}

body.theme-v2 .bg-red-500\/20.text-red-400.border-red-500\/30 {
  background: #fee2e2 !important;
  color: #dc2626 !important;
  border-color: #fecaca !important;
}

/* V2 Theme Toggle Fixes */
body.theme-v2 .fixed.bottom-6.right-6 .bg-slate-800\/90 {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

body.theme-v2 .fixed.bottom-6.right-6 .text-slate-200,
body.theme-v2 .fixed.bottom-6.right-6 .text-slate-300 {
  color: #374151 !important;
}

body.theme-v2 .fixed.bottom-6.right-6 .text-slate-400 {
  color: #6b7280 !important;
}

body.theme-v2 .fixed.bottom-6.right-6 .bg-slate-700 {
  background: #f3f4f6 !important;
  color: #374151 !important;
}

body.theme-v2 .fixed.bottom-6.right-6 .bg-slate-800 {
  background: #ffffff !important;
  color: #374151 !important;
}

/* V2 Auth Page Fixes */
body.theme-v2 .bg-slate-800\/50.border-slate-700\/50.backdrop-blur-sm {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid #e5e7eb !important;
}

/* V2 "OR CONTINUE WITH" Text Fix */
body.theme-v2 .bg-slate-800.px-2.text-slate-400 {
  background: #ffffff !important;
  color: #6b7280 !important;
}

/* V2 Auth Form Text Fixes */
body.theme-v2 .text-slate-400 {
  color: #6b7280 !important;
}

body.theme-v2 .text-slate-300 {
  color: #374151 !important;
}

/* V2 Input Field Fixes */
body.theme-v2 .bg-slate-700\/50.border-slate-600 {
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #000000 !important;
}

body.theme-v2 .bg-slate-700\/50.border-slate-600::placeholder {
  color: #9ca3af !important;
}

/* V2 Header Icon Fixes - Ultra Specific */
body.theme-v2 header button[variant="ghost"] svg,
body.theme-v2 header .text-slate-300.hover\:text-white svg,
body.theme-v2 header .text-slate-300 svg,
body.theme-v2 header button svg {
  color: #374151 !important;
  stroke: #374151 !important;
  fill: none !important;
  opacity: 1 !important;
  visibility: visible !important;
}

body.theme-v2 header button:hover svg,
body.theme-v2 header .text-slate-300.hover\:text-white:hover svg,
body.theme-v2 header .text-slate-300:hover svg,
body.theme-v2 header button:hover svg {
  color: #000000 !important;
  stroke: #000000 !important;
}

/* V2 Specific Lucide Icons in Header */
body.theme-v2 header .lucide-bell,
body.theme-v2 header .lucide-settings,
body.theme-v2 header .lucide-log-out,
body.theme-v2 header [data-lucide="bell"],
body.theme-v2 header [data-lucide="settings"],
body.theme-v2 header [data-lucide="log-out"] {
  color: #374151 !important;
  stroke: #374151 !important;
  fill: none !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

/* V2 Header Button Text Colors */
body.theme-v2 header button.text-slate-300,
body.theme-v2 header .text-slate-300 {
  color: #374151 !important;
}

body.theme-v2 header button.text-slate-300:hover,
body.theme-v2 header .text-slate-300:hover {
  color: #000000 !important;
}

/* V2 Global Icon Visibility Fix - Nuclear Option */
body.theme-v2 svg[class*="lucide"],
body.theme-v2 [class*="lucide"],
body.theme-v2 svg {
  opacity: 1 !important;
  visibility: visible !important;
  display: inline-block !important;
}

/* V2 Header Icons - Final Override */
body.theme-v2 header * {
  opacity: 1 !important;
  visibility: visible !important;
}

body.theme-v2 header svg,
body.theme-v2 header [class*="lucide"] {
  color: #374151 !important;
  stroke: #374151 !important;
  fill: none !important;
}

body.theme-v2 header button:hover svg,
body.theme-v2 header button:hover [class*="lucide"] {
  color: #000000 !important;
  stroke: #000000 !important;
}

/* V2 Deal Detail Page Background Fix */
body.theme-v2 .min-h-screen.bg-gradient-to-br.from-slate-900.via-slate-800.to-slate-900 {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%) !important;
}

/* V2 Deal Detail Cards */
body.theme-v2 .bg-slate-800\/50.border-slate-700\/50 {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* V2 Deal Detail Button Fixes */
body.theme-v2 .border-slate-600.text-white {
  border-color: #d1d5db !important;
  color: #000000 !important;
  background: #ffffff !important;
}

body.theme-v2 .border-slate-600.text-white:hover {
  background: #f3f4f6 !important;
  color: #000000 !important;
}

/* V2 Deal Detail Badge Fixes */
body.theme-v2 .border-slate-600.text-slate-400 {
  border-color: #d1d5db !important;
  color: #6b7280 !important;
  background: #f9fafb !important;
}

/* V2 Consultant/Discovery Call Page Fixes */
body.theme-v2 .bg-slate-800\/50.border-slate-700\/50.backdrop-blur-sm {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid #e5e7eb !important;
  backdrop-filter: blur(8px) !important;
}

/* V2 Card Content and Text Fixes */
body.theme-v2 .text-slate-300,
body.theme-v2 .text-slate-400 {
  color: #6b7280 !important;
}

body.theme-v2 .text-slate-200 {
  color: #374151 !important;
}

/* V2 Icon Background Fixes */
body.theme-v2 .w-12.h-12.bg-blue-500\/20,
body.theme-v2 .w-12.h-12.bg-emerald-500\/20,
body.theme-v2 .w-12.h-12.bg-amber-500\/20,
body.theme-v2 .w-12.h-12.bg-purple-500\/20,
body.theme-v2 .w-12.h-12.bg-red-500\/20 {
  background: #f3f4f6 !important;
  border: 1px solid #e5e7eb !important;
}

/* V2 Icon Colors */
body.theme-v2 .w-12.h-12 svg,
body.theme-v2 .w-12.h-12 .lucide {
  color: #374151 !important;
  stroke: #374151 !important;
}

/* V2 Avatar/Profile Icon Fixes */
body.theme-v2 .w-10.h-10.bg-slate-600,
body.theme-v2 .w-12.h-12.bg-slate-600 {
  background: #e5e7eb !important;
  color: #374151 !important;
}

/* V2 Rating Stars */
body.theme-v2 .text-yellow-400 {
  color: #f59e0b !important;
}

/* V2 Badge Fixes for Consultant Cards */
body.theme-v2 .bg-blue-500\/10.text-blue-400,
body.theme-v2 .bg-emerald-500\/10.text-emerald-400,
body.theme-v2 .bg-purple-500\/10.text-purple-400 {
  background: #f3f4f6 !important;
  color: #374151 !important;
  border: 1px solid #d1d5db !important;
}

/* V2 Advisor Card Fixes */
body.theme-v2 .bg-slate-700 {
  background: #f3f4f6 !important;
  color: #374151 !important;
}

/* V2 Calendar Icon and Date Picker Fixes */
body.theme-v2 .w-4.h-4.text-slate-400,
body.theme-v2 .w-5.h-5.text-slate-400 {
  color: #6b7280 !important;
}

/* V2 Input Field Background */
body.theme-v2 input[type="date"],
body.theme-v2 input[type="time"],
body.theme-v2 input {
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #000000 !important;
}

/* V2 Select Dropdown */
body.theme-v2 select {
  background: #ffffff !important;
  border: 1px solid #d1d5db !important;
  color: #000000 !important;
}

/* V2 Upcoming Calls Card */
body.theme-v2 .bg-slate-700\/50 {
  background: #f9fafb !important;
  border: 1px solid #e5e7eb !important;
}

/* V2 Call Card Icons */
body.theme-v2 .w-4.h-4.text-slate-500,
body.theme-v2 .w-5.h-5.text-slate-500 {
  color: #6b7280 !important;
}

/* V2 Why Schedule Call Section */
body.theme-v2 .w-6.h-6.text-emerald-400 {
  color: #059669 !important;
}

/* Theme-aware Logo Switching */
.logo-theme-aware {
  content: url('/tenxcfo-dark.png');
}

body.theme-v2 .logo-theme-aware {
  content: url('/tenxcfo.png');
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}
