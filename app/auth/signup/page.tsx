"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRight, Building, CheckCircle, TrendingUp, Users } from "lucide-react";
import Link from "next/link";

export default function SignUpPage() {
  const roles = [
    {
      id: "sme",
      title: "Small & Medium Enterprise",
      description: "Looking for funding and growth opportunities",
      features: [
        "Access to investors and funding opportunities",
        "Business health scoring and analytics",
        "Expert consultation and advisory services",
        "Growth acceleration programs"
      ],
      icon: Building,
      color: "blue",
      href: "/sme/signup",
      buttonText: "Start as SME"
    },
    {
      id: "investor",
      title: "Investor",
      description: "Seeking high-potential investment opportunities",
      features: [
        "Curated deal pipeline and opportunities",
        "Detailed company analytics and scoring",
        "Direct access to vetted SMEs",
        "Portfolio management tools"
      ],
      icon: TrendingUp,
      color: "emerald",
      href: "/investor/signup",
      buttonText: "Start Investing"
    },
    {
      id: "consultant",
      title: "Business Consultant",
      description: "Help SMEs grow and earn consulting fees",
      features: [
        "Connect with SMEs needing expertise",
        "Earn consulting fees and bonuses",
        "Build your professional network",
        "Access to business tools and resources"
      ],
      icon: Users,
      color: "amber",
      href: "/consultant/signup",
      buttonText: "Start Consulting"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <span className="text-xl font-bold text-white">CFOx</span>
          </Link>
          <Link href="/auth/signin" className="text-slate-300 hover:text-white transition-colors">
            Already have an account? Sign in
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-white mb-4">Choose Your Role</h1>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            Select the option that best describes you to get started with a tailored experience
          </p>
        </motion.div>

        {/* Role Selection Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {roles.map((role, index) => (
            <motion.div
              key={role.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <Card className="bg-slate-800/50 border-slate-700/50 hover:border-slate-600/50 transition-all duration-300 h-full group hover:scale-105">
                <CardHeader className="text-center pb-4">
                  <div className={`w-16 h-16 bg-${role.color}-500/20 rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                    <role.icon className={`w-8 h-8 text-${role.color}-400`} />
                  </div>
                  <CardTitle className="text-xl text-white">{role.title}</CardTitle>
                  <CardDescription className="text-slate-400">
                    {role.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    {role.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start space-x-3">
                        <CheckCircle className={`w-5 h-5 text-${role.color}-400 mt-0.5 flex-shrink-0`} />
                        <span className="text-slate-300 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Link href={role.href} className="block">
                    <Button
                      className={`w-full bg-${role.color}-600 hover:bg-${role.color}-700 text-white`}
                    >
                      {role.buttonText}
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}
