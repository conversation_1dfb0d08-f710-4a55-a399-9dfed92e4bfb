"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { motion } from "framer-motion";
import {
    ArrowLeft,
    Calendar,
    CheckCircle,
    Clock,
    Phone,
    Star,
    Users,
    Video
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function DiscoveryCallPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    revenue: "",
    goals: "",
    preferredTime: "",
    callType: "video"
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log("Discovery call scheduled:", formData);
    alert("Thank you! We'll contact you within 24 hours to schedule your discovery call.");
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/10x-growth-hack" className="flex items-center space-x-3">
              <ArrowLeft className="w-5 h-5 text-slate-400 hover:text-white transition-colors" />
              <span className="text-slate-400 hover:text-white transition-colors">Back to 10X Growth</span>
            </Link>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-amber-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <span className="text-xl font-bold text-white">CFOx</span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-amber-600 rounded-full flex items-center justify-center">
              <Phone className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-white mb-4">Schedule Your Discovery Call</h1>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            Book a free 30-minute consultation to discuss your business goals and see how our 10X Growth Program can accelerate your success.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Book Your Call
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Fill out the form below and we'll contact you within 24 hours to schedule your discovery call.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name" className="text-white">Full Name *</Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-white">Email Address *</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="company" className="text-white">Company Name *</Label>
                      <Input
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                        required
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="Your company name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone" className="text-white">Phone Number</Label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className="bg-slate-700/50 border-slate-600 text-white"
                        placeholder="+****************"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="revenue" className="text-white">Annual Revenue Range</Label>
                    <select
                      id="revenue"
                      name="revenue"
                      value={formData.revenue}
                      onChange={handleInputChange}
                      className="w-full mt-1 bg-slate-700/50 border border-slate-600 text-white rounded-md px-3 py-2"
                    >
                      <option value="">Select revenue range</option>
                      <option value="0-100K">$0 - $100K</option>
                      <option value="100K-500K">$100K - $500K</option>
                      <option value="500K-1M">$500K - $1M</option>
                      <option value="1M-5M">$1M - $5M</option>
                      <option value="5M+">$5M+</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="goals" className="text-white">What are your main business goals? *</Label>
                    <Textarea
                      id="goals"
                      name="goals"
                      value={formData.goals}
                      onChange={handleInputChange}
                      required
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="Tell us about your business goals and challenges..."
                      rows={4}
                    />
                  </div>

                  <div>
                    <Label htmlFor="preferredTime" className="text-white">Preferred Call Time</Label>
                    <Input
                      id="preferredTime"
                      name="preferredTime"
                      value={formData.preferredTime}
                      onChange={handleInputChange}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      placeholder="e.g., Weekdays 2-4 PM EST"
                    />
                  </div>

                  <div>
                    <Label className="text-white">Call Type Preference</Label>
                    <div className="flex space-x-4 mt-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="callType"
                          value="video"
                          checked={formData.callType === "video"}
                          onChange={handleInputChange}
                          className="mr-2"
                        />
                        <Video className="w-4 h-4 mr-1 text-slate-400" />
                        <span className="text-slate-300">Video Call</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="callType"
                          value="phone"
                          checked={formData.callType === "phone"}
                          onChange={handleInputChange}
                          className="mr-2"
                        />
                        <Phone className="w-4 h-4 mr-1 text-slate-400" />
                        <span className="text-slate-300">Phone Call</span>
                      </label>
                    </div>
                  </div>

                  <Button type="submit" className="w-full bg-amber-600 hover:bg-amber-700 text-white">
                    Schedule My Discovery Call
                    <Calendar className="w-4 h-4 ml-2" />
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            {/* What to Expect */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">What to Expect</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Clock className="w-5 h-5 text-amber-400 mt-0.5" />
                  <div>
                    <p className="text-white font-semibold">30-Minute Call</p>
                    <p className="text-slate-400 text-sm">Focused discussion about your business</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <Users className="w-5 h-5 text-amber-400 mt-0.5" />
                  <div>
                    <p className="text-white font-semibold">Expert Consultation</p>
                    <p className="text-slate-400 text-sm">Speak with our growth specialists</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="w-5 h-5 text-amber-400 mt-0.5" />
                  <div>
                    <p className="text-white font-semibold">Custom Strategy</p>
                    <p className="text-slate-400 text-sm">Tailored recommendations for your business</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Testimonial */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="w-4 h-4 text-amber-400 fill-current" />
                  ))}
                </div>
                <p className="text-slate-300 text-sm mb-4">
                  "The discovery call was incredibly valuable. They understood our challenges immediately and provided actionable insights that we implemented right away."
                </p>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-amber-600 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-xs font-bold">JS</span>
                  </div>
                  <div>
                    <p className="text-white text-sm font-semibold">John Smith</p>
                    <p className="text-slate-400 text-xs">CEO, TechStart Inc.</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <Card className="bg-slate-800/50 border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-white">Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-400 text-sm mb-4">
                  Have questions about the 10X Growth Program? Contact us directly.
                </p>
                <div className="space-y-2 text-sm">
                  <p className="text-slate-300">📧 <EMAIL></p>
                  <p className="text-slate-300">📞 +****************</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
