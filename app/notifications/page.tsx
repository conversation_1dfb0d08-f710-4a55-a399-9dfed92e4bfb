"use client";

import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowLeft
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function NotificationsPage() {
  const { user } = useAuthStore();
  const [filter, setFilter] = useState("all");

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <header className="border-b border-slate-700/50 bg-slate-900/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/sme/dashboard" className="flex items-center space-x-3">
              <ArrowLeft className="w-5 h-5 text-slate-400 hover:text-white transition-colors" />
              <span className="text-slate-400 hover:text-white transition-colors">Back to Dashboard</span>
            </Link>
          </div>
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">CF</span>
            </div>
            <span className="text-xl font-bold text-white">CFOx</span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <h1 className="text-3xl font-bold text-white mb-4">Notifications</h1>
          <p className="text-slate-400">Stay updated with your latest activities and alerts</p>
        </motion.div>
      </div>
    </div>
  );
}