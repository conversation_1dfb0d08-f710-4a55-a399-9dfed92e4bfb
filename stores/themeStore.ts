import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type ThemeVersion = 'v1' | 'v2';

interface ThemeState {
  version: ThemeVersion;
  isHydrated: boolean;
  setTheme: (version: ThemeVersion) => void;
  toggleTheme: () => void;
  setHydrated: () => void;
}

const applyThemeToBody = (version: ThemeVersion) => {
  if (typeof window === 'undefined') return;

  const body = document.body;

  // Remove existing theme classes
  body.classList.remove('theme-v1', 'theme-v2', 'light', 'dark');

  // Add new theme class
  body.classList.add(`theme-${version}`);

  // Also apply light/dark class for existing CSS variables
  if (version === 'v2') {
    body.classList.add('light');
  } else {
    body.classList.add('dark');
  }
};

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      version: 'v1', // Default to V1 (current dark theme)
      isHydrated: false,

      setTheme: (version: ThemeVersion) => {
        set({ version });
        applyThemeToBody(version);
      },

      toggleTheme: () => {
        const currentVersion = get().version;
        const newVersion = currentVersion === 'v1' ? 'v2' : 'v1';
        get().setTheme(newVersion);
      },

      setHydrated: () => {
        set({ isHydrated: true });
        // Apply theme after hydration
        const state = get();
        applyThemeToBody(state.version);
      },
    }),
    {
      name: 'theme-storage',
      partialize: (state) => ({ version: state.version }),
    }
  )
);
