#!/bin/bash

# CFOx S3 Deployment Script
# This script builds the Next.js app and deploys it to AWS S3

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUCKET_NAME="CFOx"
REGION="ap-south-1"  # Asia Pacific (Mumbai) region
CLOUDFRONT_DISTRIBUTION_ID="EAHXVWDND4JQE"  # CloudFront distribution ID
CLOUDFRONT_DOMAIN="d2tcjdwexogrvt.cloudfront.net"  # CloudFront domain name

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if AWS CLI is installed
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first:"
        echo "  brew install awscli  # On macOS"
        echo "  pip install awscli   # Using pip"
        exit 1
    fi
    print_success "AWS CLI is installed"
}

# Check if AWS credentials are configured
check_aws_credentials() {
    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS credentials are not configured. Please run:"
        echo "  aws configure"
        echo "Or set environment variables:"
        echo "  export AWS_ACCESS_KEY_ID=your_access_key"
        echo "  export AWS_SECRET_ACCESS_KEY=your_secret_key"
        exit 1
    fi
    print_success "AWS credentials are configured"
}

# Check if bucket exists
check_bucket() {
    if aws s3 ls "s3://$BUCKET_NAME" &> /dev/null; then
        print_success "S3 bucket '$BUCKET_NAME' exists"
    else
        print_error "S3 bucket '$BUCKET_NAME' does not exist or you don't have access"
        echo "Please create the bucket first or check your permissions"
        exit 1
    fi
}

# Check if Node.js and npm/pnpm are installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    # Check for package manager (prefer pnpm, fallback to npm)
    if command -v pnpm &> /dev/null; then
        PACKAGE_MANAGER="pnpm"
        print_success "Node.js and pnpm are installed"
    elif command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        print_success "Node.js and npm are installed"
    else
        print_error "No package manager found. Please install npm or pnpm."
        exit 1
    fi
}

# Build the Next.js application
build_app() {
    print_status "Building Next.js application..."
    
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Are you in the correct directory?"
        exit 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing dependencies with $PACKAGE_MANAGER..."
        $PACKAGE_MANAGER install
    fi
    
    # Build the Next.js application for static export
    print_status "Building Next.js application for static export..."
    
    # First, build the application
    if $PACKAGE_MANAGER run build; then
        print_success "Build completed successfully"
    else
        print_error "Build failed. Please check your Next.js configuration."
        exit 1
    fi
    
    # Check if out directory exists (static export)
    if [ -d "out" ]; then
        BUILD_DIR="out"
        print_success "Static export found in 'out' directory"
    elif [ -d ".next" ]; then
        BUILD_DIR=".next"
        print_warning "Using .next directory - make sure static export is configured"
    else
        print_error "Build output directory not found. Please check your Next.js configuration."
        exit 1
    fi
    
    print_success "Application built successfully in '$BUILD_DIR' directory"
}

# Clean build directory of unnecessary files
clean_build_dir() {
    print_status "Cleaning build directory of unnecessary files..."

    # Remove unnecessary files from build output
    find $BUILD_DIR -name "*.txt" -type f -delete 2>/dev/null || true
    find $BUILD_DIR -name "next.svg" -type f -delete 2>/dev/null || true
    find $BUILD_DIR -name "vercel.svg" -type f -delete 2>/dev/null || true
    find $BUILD_DIR -name "file.svg" -type f -delete 2>/dev/null || true
    find $BUILD_DIR -name "globe.svg" -type f -delete 2>/dev/null || true
    find $BUILD_DIR -name "window.svg" -type f -delete 2>/dev/null || true
    find $BUILD_DIR -name "*.map" -type f -delete 2>/dev/null || true
    find $BUILD_DIR -name ".DS_Store" -type f -delete 2>/dev/null || true

    # Count remaining files
    file_count=$(find $BUILD_DIR -type f | wc -l)
    print_success "Build directory cleaned. $file_count files ready for deployment"
}

# Deploy to S3
deploy_to_s3() {
    print_status "Deploying to S3 bucket: $BUCKET_NAME"

    # Sync static assets with long cache (JS, CSS, images with hashes)
    if [ -d "$BUILD_DIR/_next/static" ]; then
        print_status "Uploading Next.js static assets with long cache..."
        aws s3 sync $BUILD_DIR/_next/static/ s3://$BUCKET_NAME/_next/static/ \
            --delete \
            --cache-control "public, max-age=31536000, immutable" \
            --metadata-directive REPLACE
    fi

    # Upload images with medium cache
    if [ -d "$BUILD_DIR/images" ]; then
        print_status "Uploading images with medium cache..."
        aws s3 sync $BUILD_DIR/images/ s3://$BUCKET_NAME/images/ \
            --delete \
            --cache-control "public, max-age=86400" \
            --metadata-directive REPLACE
    fi

    # Upload favicon and other root assets
    if [ -f "$BUILD_DIR/favicon.ico" ]; then
        print_status "Uploading favicon..."
        aws s3 cp $BUILD_DIR/favicon.ico s3://$BUCKET_NAME/favicon.ico \
            --cache-control "public, max-age=86400" \
            --metadata-directive REPLACE
    fi

    # Upload all other files with short cache, excluding unnecessary files
    print_status "Uploading HTML pages and other files..."
    aws s3 sync $BUILD_DIR/ s3://$BUCKET_NAME/ \
        --delete \
        --exclude "_next/static/*" \
        --exclude "images/*" \
        --exclude "*.txt" \
        --exclude "next.svg" \
        --exclude "vercel.svg" \
        --exclude "file.svg" \
        --exclude "globe.svg" \
        --exclude "window.svg" \
        --exclude "*.map" \
        --exclude "*.DS_Store" \
        --exclude "*.log" \
        --cache-control "public, max-age=3600" \
        --metadata-directive REPLACE

    # Set specific cache control for index.html and other HTML files (no cache)
    print_status "Setting no-cache for HTML files..."
    find $BUILD_DIR -name "*.html" -type f | while read file; do
        relative_path=${file#$BUILD_DIR/}
        aws s3 cp "$file" s3://$BUCKET_NAME/$relative_path \
            --cache-control "no-cache, no-store, must-revalidate" \
            --metadata-directive REPLACE
    done

    print_success "Files uploaded to S3"

    # Show deployment summary
    print_status "Deployment Summary:"
    html_count=$(find $BUILD_DIR -name "*.html" -type f | wc -l)
    js_count=$(find $BUILD_DIR -name "*.js" -type f | wc -l)
    css_count=$(find $BUILD_DIR -name "*.css" -type f | wc -l)
    total_size=$(du -sh $BUILD_DIR | cut -f1)

    echo "  📄 HTML files: $html_count"
    echo "  🟨 JavaScript files: $js_count"
    echo "  🎨 CSS files: $css_count"
    echo "  📦 Total size: $total_size"
}

# Configure S3 bucket for static website hosting
configure_website() {
    print_status "Configuring S3 bucket for static website hosting..."
    
    # Create website configuration
    cat > website-config.json << EOF
{
    "IndexDocument": {
        "Suffix": "index.html"
    },
    "ErrorDocument": {
        "Key": "index.html"
    }
}
EOF
    
    # Apply website configuration
    aws s3api put-bucket-website \
        --bucket $BUCKET_NAME \
        --website-configuration file://website-config.json
    
    # Clean up temp file
    rm website-config.json
    
    print_success "Website configuration applied"
}

# Set bucket policy for public read access
set_bucket_policy() {
    print_status "Setting bucket policy for public read access..."
    
    # Create bucket policy
    cat > bucket-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::$BUCKET_NAME/*"
        }
    ]
}
EOF
    
    # Apply bucket policy
    aws s3api put-bucket-policy \
        --bucket $BUCKET_NAME \
        --policy file://bucket-policy.json
    
    # Clean up temp file
    rm bucket-policy.json
    
    print_success "Bucket policy applied"
}

# Invalidate CloudFront cache
invalidate_cloudfront() {
    print_status "Invalidating CloudFront cache..."
    aws cloudfront create-invalidation \
        --distribution-id $CLOUDFRONT_DISTRIBUTION_ID \
        --paths "/*"
    print_success "CloudFront invalidation created"
}

# Get website URL
get_website_url() {
    WEBSITE_URL="http://$BUCKET_NAME.s3-website.$REGION.amazonaws.com"
    print_success "Deployment complete!"
    echo ""
    echo "🌐 Website URL: $WEBSITE_URL"
    echo "🌐 S3 Origin URL: https://$BUCKET_NAME.s3.$REGION.amazonaws.com"
    echo "🚀 CloudFront URL: https://$CLOUDFRONT_DOMAIN"
    echo "📊 S3 Console: https://s3.console.aws.amazon.com/s3/buckets/$BUCKET_NAME"
    echo "☁️  CloudFront Console: https://console.aws.amazon.com/cloudfront/v3/home?region=us-east-1#/distributions/$CLOUDFRONT_DISTRIBUTION_ID"
    echo ""
    echo "Note: It may take a few minutes for changes to propagate."
    echo "CloudFront cache invalidation may take 10-15 minutes to complete."
}

# Main deployment function
main() {
    echo "🚀 CFOx S3 Deployment Script"
    echo "============================="
    echo ""
    
    # Run all checks and deployment steps
    check_aws_cli
    check_aws_credentials
    check_node
    check_bucket
    build_app
    clean_build_dir
    deploy_to_s3
    configure_website
    set_bucket_policy
    invalidate_cloudfront
    get_website_url
}

# Run main function
main "$@"
